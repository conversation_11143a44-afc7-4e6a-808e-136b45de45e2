<?php
/**
 * CodeIgniter
 *
 * An open source application development framework for PHP
 *
 * This content is released under the MIT License (MIT)
 *
 * Copyright (c) 2014 - 2019, British Columbia Institute of Technology
 *
 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to deal
 * in the Software without restriction, including without limitation the rights
 * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in
 * all copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
 * THE SOFTWARE.
 *
 * @package    CodeIgniter
 * <AUTHOR> Dev Team
 * @copyright    Copyright (c) 2008 - 2014, EllisLab, Inc. (https://ellislab.com/)
 * @copyright    Copyright (c) 2014 - 2019, British Columbia Institute of Technology (https://bcit.ca/)
 * @license    https://opensource.org/licenses/MIT	MIT License
 * @link    https://codeigniter.com
 * @since    Version 1.0.0
 * @filesource
 */
defined('BASEPATH') or exit('No direct script access allowed');

$lang['profiler_database'] = 'DATABASE';
$lang['profiler_controller_info'] = 'CLASS/METHOD';
$lang['profiler_benchmarks'] = 'BENCHMARKS';
$lang['profiler_queries'] = 'QUERIES';
$lang['profiler_get_data'] = 'GET DATA';
$lang['profiler_post_data'] = 'POST DATA';
$lang['profiler_uri_string'] = 'URI STRING';
$lang['profiler_memory_usage'] = 'MEMORY USAGE';
$lang['profiler_config'] = 'CONFIG VARIABLES';
$lang['profiler_session_data'] = 'SESSION DATA';
$lang['profiler_headers'] = 'HTTP HEADERS';
$lang['profiler_no_db'] = 'Database driver is not currently loaded';
$lang['profiler_no_queries'] = 'No queries were run';
$lang['profiler_no_post'] = 'No POST data exists';
$lang['profiler_no_get'] = 'No GET data exists';
$lang['profiler_no_uri'] = 'No URI data exists';
$lang['profiler_no_memory'] = 'Memory Usage Unavailable';
$lang['profiler_no_profiles'] = 'No Profile data - all Profiler sections have been disabled.';
$lang['profiler_section_hide'] = 'Hide';
$lang['profiler_section_show'] = 'Show';
$lang['profiler_seconds'] = 'seconds';
