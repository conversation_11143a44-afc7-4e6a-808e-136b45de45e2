<?php
/**
 * CodeIgniter
 *
 * An open source application development framework for PHP
 *
 * This content is released under the MIT License (MIT)
 *
 * Copyright (c) 2014 - 2019, British Columbia Institute of Technology
 *
 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to deal
 * in the Software without restriction, including without limitation the rights
 * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in
 * all copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
 * THE SOFTWARE.
 *
 * @package    CodeIgniter
 * <AUTHOR> Dev Team
 * @copyright    Copyright (c) 2008 - 2014, EllisLab, Inc. (https://ellislab.com/)
 * @copyright    Copyright (c) 2014 - 2019, British Columbia Institute of Technology (https://bcit.ca/)
 * @license    https://opensource.org/licenses/MIT	MIT License
 * @link    https://codeigniter.com
 * @since    Version 1.0.0
 * @filesource
 */
defined('BASEPATH') or exit('No direct script access allowed');

$lang['cal_su'] = 'Di';
$lang['cal_mo'] = 'Hë';
$lang['cal_tu'] = 'Ma';
$lang['cal_we'] = 'Më';
$lang['cal_th'] = 'En';
$lang['cal_fr'] = 'Pr';
$lang['cal_sa'] = 'Sh';
$lang['cal_sun'] = 'Die';
$lang['cal_mon'] = 'Hën';
$lang['cal_tue'] = 'Mar';
$lang['cal_wed'] = 'Mër';
$lang['cal_thu'] = 'Enj';
$lang['cal_fri'] = 'Pre';
$lang['cal_sat'] = 'Sht';
$lang['cal_sunday'] = 'E Diel';
$lang['cal_monday'] = 'E Hënë';
$lang['cal_tuesday'] = 'E Martë';
$lang['cal_wednesday'] = 'E Mërkurë';
$lang['cal_thursday'] = 'E Enjte';
$lang['cal_friday'] = 'E Premte';
$lang['cal_saturday'] = 'E Shtunë';
$lang['cal_jan'] = 'Jan';
$lang['cal_feb'] = 'Shk';
$lang['cal_mar'] = 'Mar';
$lang['cal_apr'] = 'Pri';
$lang['cal_may'] = 'Maj';
$lang['cal_jun'] = 'Qer';
$lang['cal_jul'] = 'Kor';
$lang['cal_aug'] = 'Gus';
$lang['cal_sep'] = 'Sht';
$lang['cal_oct'] = 'Tet';
$lang['cal_nov'] = 'Nën';
$lang['cal_dec'] = 'Dhj';
$lang['cal_january'] = 'Janar';
$lang['cal_february'] = 'Shkurt';
$lang['cal_march'] = 'Mars';
$lang['cal_april'] = 'Prill';
$lang['cal_mayl'] = 'Maj';
$lang['cal_june'] = 'Qershor';
$lang['cal_july'] = 'Korrik';
$lang['cal_august'] = 'Gusht';
$lang['cal_september'] = 'Shtator';
$lang['cal_october'] = 'Tetor';
$lang['cal_november'] = 'Nëntor';
$lang['cal_december'] = 'Dhjetor';
