<?php
/**
 * CodeIgniter
 *
 * An open source application development framework for PHP
 *
 * This content is released under the MIT License (MIT)
 *
 * Copyright (c) 2014 - 2019, British Columbia Institute of Technology
 *
 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to deal
 * in the Software without restriction, including without limitation the rights
 * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in
 * all copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
 * THE SOFTWARE.
 *
 * @package    CodeIgniter
 * <AUTHOR> Dev Team
 * @copyright    Copyright (c) 2008 - 2014, EllisLab, Inc. (https://ellislab.com/)
 * @copyright    Copyright (c) 2014 - 2019, British Columbia Institute of Technology (https://bcit.ca/)
 * @license    https://opensource.org/licenses/MIT	MIT License
 * @link    https://codeigniter.com
 * @since    Version 1.0.0
 * @filesource
 */
defined('BASEPATH') or exit('No direct script access allowed');

$lang['form_validation_required'] = 'Fusha {field} është e detyrueshme.';
$lang['form_validation_isset'] = 'Fusha {field} duhet të ketë një vlerë.';
$lang['form_validation_valid_email'] = 'Fusha {field} duhet të përmbajë një adresë email-i të vlefshme.';
$lang['form_validation_valid_emails'] = 'Fusha {field} duhet të përmbajë të gjitha adresat e vlefshme të email-it.';
$lang['form_validation_valid_url'] = 'Fusha {field} duhet të përmbajë një URL të vlefshme.';
$lang['form_validation_valid_ip'] = 'Fusha {field} duhet të përmbajë një IP të vlefshme.';
$lang['form_validation_valid_base64'] = 'Fusha {field} duhet të përmbajë një varg Base64 të vlefshëm.';
$lang['form_validation_min_length'] = 'Fusha {field} duhet të jetë të paktën {param} karaktere e gjatë.';
$lang['form_validation_max_length'] = 'Fusha {field} nuk mund të jetë më e gjatë se {param} karaktere.';
$lang['form_validation_exact_length'] = 'Fusha {field} duhet të jetë saktësisht {param} karaktere e gjatë.';
$lang['form_validation_alpha'] = 'Fusha {field} mund të përmbajë vetëm karaktere alfabetike.';
$lang['form_validation_alpha_numeric'] = 'Fusha {field} mund të përmbajë vetëm karaktere alfa-numerike.';
$lang['form_validation_alpha_numeric_spaces'] = 'Fusha {field} mund të përmbajë vetëm karaktere alfa-numerike dhe hapësira.';
$lang['form_validation_alpha_dash'] = 'Fusha {field} mund të përmbajë vetëm karaktere alfa-numerike, vija të poshtme dhe viza.';
$lang['form_validation_numeric'] = 'Fusha {field} duhet të përmbajë vetëm numra.';
$lang['form_validation_is_numeric'] = 'Fusha {field} duhet të përmbajë vetëm karaktere numerike.';
$lang['form_validation_integer'] = 'Fusha {field} duhet të përmbajë një numër të plotë.';
$lang['form_validation_regex_match'] = 'Fusha {field} nuk është në formatin e saktë.';
$lang['form_validation_matches'] = 'Fusha {field} nuk përputhet me fushën {param}.';
$lang['form_validation_differs'] = 'Fusha {field} duhet të jetë e ndryshme nga fusha {param}.';
$lang['form_validation_is_unique'] = 'Fusha {field} duhet të përmbajë një vlerë unike.';
$lang['form_validation_is_natural'] = 'Fusha {field} duhet të përmbajë vetëm shifra.';
$lang['form_validation_is_natural_no_zero'] = 'Fusha {field} duhet të përmbajë vetëm shifra dhe duhet të jetë më e madhe se zero.';
$lang['form_validation_decimal'] = 'Fusha {field} duhet të përmbajë një numër dhjetor.';
$lang['form_validation_less_than'] = 'Fusha {field} duhet të përmbajë një numër më të vogël se {param}.';
$lang['form_validation_less_than_equal_to'] = 'Fusha {field} duhet të përmbajë një numër më të vogël ose të barabartë me {param}.';
$lang['form_validation_greater_than'] = 'Fusha {field} duhet të përmbajë një numër më të madh se {param}.';
$lang['form_validation_greater_than_equal_to'] = 'Fusha {field} duhet të përmbajë një numër më të madh ose të barabartë me {param}.';
$lang['form_validation_error_message_not_set'] = 'Nuk mund të qaset një mesazh gabimi që korrespondon me emrin e fushës suaj {field}.';
$lang['form_validation_in_list'] = 'Fusha {field} duhet të jetë një nga: {param}.';
