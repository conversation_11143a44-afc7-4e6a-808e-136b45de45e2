<?php
/**
 * CodeIgniter
 *
 * An open source application development framework for PHP
 *
 * This content is released under the MIT License (MIT)
 *
 * Copyright (c) 2014 - 2019, British Columbia Institute of Technology
 *
 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to deal
 * in the Software without restriction, including without limitation the rights
 * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in
 * all copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
 * THE SOFTWARE.
 *
 * @package    CodeIgniter
 * <AUTHOR> Dev Team
 * @copyright    Copyright (c) 2008 - 2014, EllisLab, Inc. (https://ellislab.com/)
 * @copyright    Copyright (c) 2014 - 2019, British Columbia Institute of Technology (https://bcit.ca/)
 * @license    https://opensource.org/licenses/MIT	MIT License
 * @link    https://codeigniter.com
 * @since    Version 1.0.0
 * @filesource
 */
defined('BASEPATH') or exit('No direct script access allowed');

$lang['form_validation_required'] = 'Feltet {field} er påkrevd.';
$lang['form_validation_isset'] = 'Feltet {field} må ha en verdi';
$lang['form_validation_isset'] = 'Feltet {field} må ha en verdi.';
$lang['form_validation_valid_email'] = 'Feltet {field} må inneholde en gyldig e-postadresse.';
$lang['form_validation_valid_emails'] = 'Feltet {field} må inneholde alle gyldige e-postadresser.';
$lang['form_validation_valid_url'] = 'Feltet {field} må inneholde en gyldig URL.';
$lang['form_validation_valid_ip'] = 'Feltet {field} må inneholde en gyldig IP.';
$lang['form_validation_valid_base64'] = 'Feltet {field} må inneholde en gyldig Base64-streng.';
$lang['form_validation_min_length'] = 'Feltet {field} må være minst {param} tegn langt.';
$lang['form_validation_max_length'] = 'Feltet {field} kan ikke overstige {param} tegn i lengde.';
$lang['form_validation_exact_length'] = 'Feltet {field} må være nøyaktig {param} tegn langt.';
$lang['form_validation_alpha'] = '{field}-feltet kan bare inneholde alfabetiske tegn.';
$lang['form_validation_alpha_numeric'] = 'Feltet {field} kan bare inneholde alfanumeriske tegn.';
$lang['form_validation_alpha_numeric_spaces'] = 'Feltet {field} kan bare inneholde alfanumeriske tegn og mellomrom.';
$lang['form_validation_alpha_dash'] = 'Feltet {field} kan bare inneholde alfanumeriske tegn, understrekninger og bindestreker.';
$lang['form_validation_numeric'] = 'Feltet {field} må bare inneholde tall.';
$lang['form_validation_is_numeric'] = 'Feltet {field} må bare inneholde numeriske tegn.';
$lang['form_validation_integer'] = 'Feltet {field} må inneholde et heltall.';
$lang['form_validation_regex_match'] = 'Feltet {field} er ikke i riktig format.';
$lang['form_validation_matches'] = '{field}-feltet samsvarer ikke med {param}-feltet.';
$lang['form_validation_differs'] = '{field}-feltet må avvike fra {param}-feltet.';
$lang['form_validation_is_unique'] = 'Feltet {field} må inneholde en unik verdi.';
$lang['form_validation_is_natural'] = '{field}-feltet må bare inneholde sifre.';
$lang['form_validation_is_natural_no_zero'] = 'Feltet {field} må bare inneholde sifre og må være større enn null.';
$lang['form_validation_decimal'] = 'Feltet {field} må inneholde et desimaltall.';
$lang['form_validation_less_than'] = 'Feltet {field} må inneholde et tall som er mindre enn {param}.';
$lang['form_validation_less_than_equal_to'] = 'Feltet {field} må inneholde et tall som er mindre enn eller lik {param}.';
$lang['form_validation_greater_than'] = 'Feltet {field} må inneholde et tall som er større enn {param}.';
$lang['form_validation_greater_than_equal_to'] = 'Feltet {field} må inneholde et tall som er større enn eller lik {param}.';
$lang['form_validation_error_message_not_set'] = 'Kan ikke få tilgang til en feilmelding som tilsvarer feltnavnet {field}.';
$lang['form_validation_in_list'] = '{field}-feltet må være ett av: {param}.';
