<?php
/**
 * CodeIgniter
 *
 * An open source application development framework for PHP
 *
 * This content is released under the MIT License (MIT)
 *
 * Copyright (c) 2014 - 2019, British Columbia Institute of Technology
 *
 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to deal
 * in the Software without restriction, including without limitation the rights
 * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in
 * all copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
 * THE SOFTWARE.
 *
 * @package    CodeIgniter
 * <AUTHOR> Dev Team
 * @copyright    Copyright (c) 2008 - 2014, EllisLab, Inc. (https://ellislab.com/)
 * @copyright    Copyright (c) 2014 - 2019, British Columbia Institute of Technology (https://bcit.ca/)
 * @license    https://opensource.org/licenses/MIT	MIT License
 * @link    https://codeigniter.com
 * @since    Version 1.0.0
 * @filesource
 */
defined('BASEPATH') or exit('No direct script access allowed');

$lang['cal_su'] = 'Pr';
$lang['cal_mo'] = 'Pt';
$lang['cal_tu'] = 'Sa';
$lang['cal_we'] = 'Ça';
$lang['cal_th'] = 'Pe';
$lang['cal_fr'] = 'Cu';
$lang['cal_sa'] = 'Ct';
$lang['cal_sun'] = 'Pzr';
$lang['cal_mon'] = 'Pzt';
$lang['cal_tue'] = 'Sal';
$lang['cal_wed'] = 'Çar';
$lang['cal_thu'] = 'Per';
$lang['cal_fri'] = 'Cum';
$lang['cal_sat'] = 'Cmt';
$lang['cal_sunday'] = 'Pazar';
$lang['cal_monday'] = 'Pazartesi';
$lang['cal_tuesday'] = 'Salı';
$lang['cal_wednesday'] = 'Çarşamba';
$lang['cal_thursday'] = 'Perşembe';
$lang['cal_friday'] = 'Cuma';
$lang['cal_saturday'] = 'Cumartesi';
$lang['cal_jan'] = 'Ock';
$lang['cal_feb'] = 'Şbt';
$lang['cal_mar'] = 'Mrt';
$lang['cal_apr'] = 'Nsn';
$lang['cal_may'] = 'Mys';
$lang['cal_jun'] = 'Haz';
$lang['cal_jul'] = 'Tem';
$lang['cal_aug'] = 'Ags';
$lang['cal_sep'] = 'Eyl';
$lang['cal_oct'] = 'Ekm';
$lang['cal_nov'] = 'Ksm';
$lang['cal_dec'] = 'Ara';
$lang['cal_january'] = 'Ocak';
$lang['cal_february'] = 'Şubat';
$lang['cal_march'] = 'Mart';
$lang['cal_april'] = 'Nisan';
$lang['cal_mayl'] = 'Mayıs';
$lang['cal_june'] = 'Haziran';
$lang['cal_july'] = 'Temmuz';
$lang['cal_august'] = 'Ağustos';
$lang['cal_september'] = 'Eylül';
$lang['cal_october'] = 'Ekim';
$lang['cal_november'] = 'Kasım';
$lang['cal_december'] = 'Aralık';
