<?php
/**
 * CodeIgniter
 *
 * An open source application development framework for PHP
 *
 * This content is released under the MIT License (MIT)
 *
 * Copyright (c) 2014 - 2019, British Columbia Institute of Technology
 *
 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to deal
 * in the Software without restriction, including without limitation the rights
 * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in
 * all copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
 * THE SOFTWARE.
 *
 * @package    CodeIgniter
 * <AUTHOR> Dev Team
 * @copyright    Copyright (c) 2008 - 2014, EllisLab, Inc. (https://ellislab.com/)
 * @copyright    Copyright (c) 2014 - 2019, British Columbia Institute of Technology (https://bcit.ca/)
 * @license    https://opensource.org/licenses/MIT	MIT License
 * @link    https://codeigniter.com
 * @since    Version 1.0.0
 * @filesource
 */
defined('BASEPATH') or exit('No direct script access allowed');

$lang['date_year'] = 'Yıl';
$lang['date_years'] = 'Yıl';
$lang['date_month'] = 'Ay';
$lang['date_months'] = 'Ay';
$lang['date_week'] = 'Hafta';
$lang['date_weeks'] = 'Hafta';
$lang['date_day'] = 'Gün';
$lang['date_days'] = 'Gün';
$lang['date_hour'] = 'Saat';
$lang['date_hours'] = 'Saat';
$lang['date_minute'] = 'Dakika';
$lang['date_minutes'] = 'Dakika';
$lang['date_second'] = 'Saniye';
$lang['date_seconds'] = 'Saniye';

$lang['UM12'] = '(UTC -12:00) Baker/Howland Adası';
$lang['UM11'] = '(UTC -11:00) Niue';
$lang['UM10'] = '(UTC -10:00) Hawaii-Aleutian Standart Saati, Cook Adaları, Tahiti';
$lang['UM95'] = '(UTC -9:30) Marquesas Adaları';
$lang['UM9'] = '(UTC -9:00) Alaska Standart Saati, Gambier Adaları';
$lang['UM8'] = '(UTC -8:00) Pacific Standart Saati, Clipperton Adası';
$lang['UM7'] = '(UTC -7:00) Mountain Standart Saati';
$lang['UM6'] = '(UTC -6:00) Central Standart Saati';
$lang['UM5'] = '(UTC -5:00) Doğu Standart Saati, Batı Karayipler Standart Saati';
$lang['UM45'] = '(UTC -4:30) Venezuela Standart Saati';
$lang['UM4'] = '(UTC -4:00) Atlantic Standart Saati, Eastern Caribbean Standart Saati';
$lang['UM35'] = '(UTC -3:30) Newfoundland Standart Saati';
$lang['UM3'] = '(UTC -3:00) Arjantin, Brezilya, Fransa Guiana, Uruguay';
$lang['UM2'] = '(UTC -2:00) South Georgia/South Sandwich Adaları, Türkiye Batı Saati';
$lang['UM1'] = '(UTC -1:00) Azores, Cape Verde Adaları';
$lang['UTC'] = '(UTC) Greenwich Mean Saati, Batı Afrika Saati';
$lang['UP1'] = '(UTC +1:00) Merkez Avrupa Saati, Batı Afrika Saati';
$lang['UP2'] = '(UTC +2:00) Merkez Afrika Saati, Doğu Avrupa Saati, Kaliningrad Saati';
$lang['UP3'] = '(UTC +3:00) Moskova Saati, Doğu Afrika Saati, Arabistan Standart Saati, Türkiye Doğu Saati';
$lang['UP35'] = '(UTC +3:30) Iran Standart Saati';
$lang['UP4'] = '(UTC +4:00) Azerbaycan Standart Saati, Samara Saati';
$lang['UP45'] = '(UTC +4:30) Afghanistan';
$lang['UP5'] = '(UTC +5:00) Pakistan Standart Saati, Yekaterinburg Saati';
$lang['UP55'] = '(UTC +5:30) Indian Standart Saati, Sri Lanka Saati';
$lang['UP575'] = '(UTC +5:45) Nepal Saati';
$lang['UP6'] = '(UTC +6:00) Bangladesh Standart Saati, Bhutan Saati, Omsk Saati';
$lang['UP65'] = '(UTC +6:30) Cocos Adaları, Myanmar';
$lang['UP7'] = '(UTC +7:00) Krasnoyarsk Saati, Cambodia, Laos, Thailand, Vietnam';
$lang['UP8'] = '(UTC +8:00) Australian Western Standart Saati, Beijing Saati, Irkutsk Saati';
$lang['UP875'] = '(UTC +8:45) Australian Central Western Standart Saati';
$lang['UP9'] = '(UTC +9:00) Japan Standart Saati, Korea Standart Saati, Yakutsk Saati';
$lang['UP95'] = '(UTC +9:30) Australia Merkez Standart Saati';
$lang['UP10'] = '(UTC +10:00) Australia Doğu Standart Saati, Vladivostok Saati';
$lang['UP105'] = '(UTC +10:30) Lord Howe Adası';
$lang['UP11'] = '(UTC +11:00) Srednekolymsk Saati, Solomon Adaları, Vanuatu';
$lang['UP115'] = '(UTC +11:30) Norfolk Adası';
$lang['UP12'] = '(UTC +12:00) Fiji, Gilbert Adaları, Kamchatka Saati, Yeni Zelanda Standart Saati';
$lang['UP1275'] = '(UTC +12:45) Chatham Adaları Standart Saati';
$lang['UP13'] = '(UTC +13:00) Samoa Saat Dilimi, Phoenix Adaları Saati, Tonga';
$lang['UP14'] = '(UTC +14:00) Line Adaları';
