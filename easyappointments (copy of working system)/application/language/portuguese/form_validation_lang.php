<?php
/**
 * CodeIgniter
 *
 * An open source application development framework for PHP
 *
 * This content is released under the MIT License (MIT)
 *
 * Copyright (c) 2014 - 2019, British Columbia Institute of Technology
 *
 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to deal
 * in the Software without restriction, including without limitation the rights
 * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in
 * all copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
 * THE SOFTWARE.
 *
 * @package    CodeIgniter
 * <AUTHOR> Dev Team
 * @copyright    Copyright (c) 2008 - 2014, EllisLab, Inc. (https://ellislab.com/)
 * @copyright    Copyright (c) 2014 - 2019, British Columbia Institute of Technology (https://bcit.ca/)
 * @license    https://opensource.org/licenses/MIT	MIT License
 * @link    https://codeigniter.com
 * @since    Version 1.0.0
 * @filesource
 */
defined('BASEPATH') or exit('No direct script access allowed');

$lang['form_validation_required'] = 'O campo {field} é obrigatório.';
$lang['form_validation_isset'] = 'O campo {field} deve de ter um valor.';
$lang['form_validation_valid_email'] = 'O campo {field} deve conter um endereço de email válido.';
$lang['form_validation_valid_emails'] = 'O campo {field} field must contain all valid email addresses.';
$lang['form_validation_valid_url'] = 'O campo {field} deve conter um URL válido.';
$lang['form_validation_valid_ip'] = 'O campo {field} deve conter um IP válido.';
$lang['form_validation_valid_base64'] = 'O campo {field} deve conter uma string Base64 válida.';
$lang['form_validation_min_length'] = 'O campo {field} deve ter pelo menos {param} caracteres de comprimento.';
$lang['form_validation_max_length'] = 'O campo {field} não pode exceder {param} caracteres de comprimento.';
$lang['form_validation_exact_length'] = 'O campo {field} deve ter exatamente {param} caracteres de comprimento.';
$lang['form_validation_alpha'] = 'O campo {field} pode conter apenas caracteres alfabéticos.';
$lang['form_validation_alpha_numeric'] = 'O campo {field} pode conter apenas caracteres alfanuméricos.';
$lang['form_validation_alpha_numeric_spaces'] = 'O campo {field} pode conter apenas caracteres alfanuméricos e espaços.';
$lang['form_validation_alpha_dash'] = 'O campo {field} pode conter apenas caracteres alfanuméricos, underscores e hífens.';
$lang['form_validation_numeric'] = 'O campo {field} deve conter apenas números.';
$lang['form_validation_is_numeric'] = 'O campo {field} deve conter apenas caracteres numéricos.';
$lang['form_validation_integer'] = 'O campo {field} deve conter um número inteiro.';
$lang['form_validation_regex_match'] = 'O campo {field} não está no formato correto.';
$lang['form_validation_matches'] = 'O campo {field} não corresponde ao campo {param}.';
$lang['form_validation_differs'] = 'O campo {field} deve ser diferente do campo {param}.';
$lang['form_validation_is_unique'] = 'O campo {field} deve conter um valor único.';
$lang['form_validation_is_natural'] = 'O campo {field} deve conter apenas dígitos.';
$lang['form_validation_is_natural_no_zero'] = 'O campo {field} deve conter apenas dígitos e deve ser maior que zero.';
$lang['form_validation_decimal'] = 'O campo {field} deve conter um número decimal.';
$lang['form_validation_less_than'] = 'O campo {field} deve conter um número menor que {param}.';
$lang['form_validation_less_than_equal_to'] = 'O campo {field} deve conter um número menor ou igual a {param}.';
$lang['form_validation_greater_than'] = 'O campo {field} deve conter um número maior que {param}.';
$lang['form_validation_greater_than_equal_to'] = 'O campo {field} deve conter um número maior ou igual a {param}.';
$lang['form_validation_error_message_not_set'] = 'Não foi possível acessar uma mensagem de erro correspondente ao nome do campo {field}.';
$lang['form_validation_in_list'] = 'O campo {field} deve ser um dos seguintes: {param}.';
