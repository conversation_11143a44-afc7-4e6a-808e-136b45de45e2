<?php
/**
 * CodeIgniter
 *
 * An open source application development framework for PHP
 *
 * This content is released under the MIT License (MIT)
 *
 * Copyright (c) 2014 - 2019, British Columbia Institute of Technology
 *
 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to deal
 * in the Software without restriction, including without limitation the rights
 * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in
 * all copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
 * THE SOFTWARE.
 *
 * @package    CodeIgniter
 * <AUTHOR> Dev Team
 * @copyright    Copyright (c) 2008 - 2014, EllisLab, Inc. (https://ellislab.com/)
 * @copyright    Copyright (c) 2014 - 2019, British Columbia Institute of Technology (https://bcit.ca/)
 * @license    https://opensource.org/licenses/MIT	MIT License
 * @link    https://codeigniter.com
 * @since    Version 1.0.0
 * @filesource
 */
defined('BASEPATH') or exit('No direct script access allowed');

$lang['cal_su'] = 'Do';
$lang['cal_mo'] = 'Se';
$lang['cal_tu'] = 'Te';
$lang['cal_we'] = 'Qu';
$lang['cal_th'] = 'Qu';
$lang['cal_fr'] = 'Se';
$lang['cal_sa'] = 'Sá';
$lang['cal_sun'] = 'Dom';
$lang['cal_mon'] = 'Seg';
$lang['cal_tue'] = 'Ter';
$lang['cal_wed'] = 'Qua';
$lang['cal_thu'] = 'Qui';
$lang['cal_fri'] = 'Sex';
$lang['cal_sat'] = 'Sáb';
$lang['cal_sunday'] = 'Domingo';
$lang['cal_monday'] = 'Segunda-feira';
$lang['cal_tuesday'] = 'Terça-feira';
$lang['cal_wednesday'] = 'Quarta-feira';
$lang['cal_thursday'] = 'Quinta-feira';
$lang['cal_friday'] = 'Sexta-feira';
$lang['cal_saturday'] = 'Sábado';
$lang['cal_jan'] = 'Jan';
$lang['cal_feb'] = 'Fev';
$lang['cal_mar'] = 'Mar';
$lang['cal_apr'] = 'Abr';
$lang['cal_may'] = 'Mai';
$lang['cal_jun'] = 'Jun';
$lang['cal_jul'] = 'Jul';
$lang['cal_aug'] = 'Ago';
$lang['cal_sep'] = 'Set';
$lang['cal_oct'] = 'Out';
$lang['cal_nov'] = 'Nov';
$lang['cal_dec'] = 'Dez';
$lang['cal_january'] = 'Janeiro';
$lang['cal_february'] = 'Fevereiro';
$lang['cal_march'] = 'Março';
$lang['cal_april'] = 'Abril';
$lang['cal_mayl'] = 'Maio';
$lang['cal_june'] = 'Junho';
$lang['cal_july'] = 'Julho';
$lang['cal_august'] = 'Agosto';
$lang['cal_september'] = 'Setembro';
$lang['cal_october'] = 'Outubro';
$lang['cal_november'] = 'Novembro';
$lang['cal_december'] = 'Dezembro';
