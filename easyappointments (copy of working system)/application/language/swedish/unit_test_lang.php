<?php
/**
 * CodeIgniter
 *
 * An open source application development framework for PHP
 *
 * This content is released under the MIT License (MIT)
 *
 * Copyright (c) 2014 - 2019, British Columbia Institute of Technology
 *
 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to deal
 * in the Software without restriction, including without limitation the rights
 * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in
 * all copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
 * THE SOFTWARE.
 *
 * @package    CodeIgniter
 * <AUTHOR> Dev Team
 * @copyright    Copyright (c) 2008 - 2014, EllisLab, Inc. (https://ellislab.com/)
 * @copyright    Copyright (c) 2014 - 2019, British Columbia Institute of Technology (https://bcit.ca/)
 * @license    https://opensource.org/licenses/MIT	MIT License
 * @link    https://codeigniter.com
 * @since    Version 1.0.0
 * @filesource
 */
defined('BASEPATH') or exit('Direktaccess till skriptet är inte tillåtet');

$lang['ut_test_name'] = 'Test-namn';
$lang['ut_test_datatype'] = 'Test-datatyp';
$lang['ut_res_datatype'] = 'Förväntad datatyp';
$lang['ut_result'] = 'Resultat';
$lang['ut_undefined'] = 'Odefinerat test-namn';
$lang['ut_file'] = 'Filnamn';
$lang['ut_line'] = 'Radnummer';
$lang['ut_passed'] = 'Ok';
$lang['ut_failed'] = 'Fel';
$lang['ut_boolean'] = 'Booleskt';
$lang['ut_integer'] = 'Heltal';
$lang['ut_float'] = 'Decimaltal';
$lang['ut_double'] = 'Decimaltal'; // can be the same as float
$lang['ut_string'] = 'Sträng';
$lang['ut_array'] = 'Array';
$lang['ut_object'] = 'Objekt';
$lang['ut_resource'] = 'Resurs';
$lang['ut_null'] = 'Null';
$lang['ut_notes'] = 'Anteckningar';
