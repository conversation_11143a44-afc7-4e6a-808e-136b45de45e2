<?php
/**
 * CodeIgniter
 *
 * An open source application development framework for PHP
 *
 * This content is released under the MIT License (MIT)
 *
 * Copyright (c) 2014 - 2019, British Columbia Institute of Technology
 *
 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to deal
 * in the Software without restriction, including without limitation the rights
 * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in
 * all copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
 * THE SOFTWARE.
 *
 * @package    CodeIgniter
 * <AUTHOR> Dev Team
 * @copyright    Copyright (c) 2008 - 2014, EllisLab, Inc. (https://ellislab.com/)
 * @copyright    Copyright (c) 2014 - 2019, British Columbia Institute of Technology (https://bcit.ca/)
 * @license    https://opensource.org/licenses/MIT	MIT License
 * @link    https://codeigniter.com
 * @since    Version 1.0.0
 * @filesource
 */
defined('BASEPATH') or exit('Direktaccess till skriptet är inte tillåtet');

$lang['form_validation_required'] = 'Fältet {field} är obligatoriskt.';
$lang['form_validation_isset'] = 'Fältet {field} måste ha ett värde.';
$lang['form_validation_valid_email'] = 'Fältet {field} måste innehålla en korrekt e-postadress.';
$lang['form_validation_valid_emails'] = 'Fältet {field} måste alla innehålla korrekta e-postadresser.';
$lang['form_validation_valid_url'] = 'Fältet {field} måste innehålla en korrekt URL.';
$lang['form_validation_valid_ip'] = 'Fältet {field} måste innehålla en korrekt IP-adress.';
$lang['form_validation_valid_base64'] = 'Fältet {field} måste innehålla en korrekt Base64-sträng.';
$lang['form_validation_min_length'] = 'Fältet {field} måste innehålla minst {param} tecken.';
$lang['form_validation_max_length'] = 'Fältet {field} kan inte överstiga {param} tecken.';
$lang['form_validation_exact_length'] = 'Fältet {field} måste innehålla exakt {param} tecken.';
$lang['form_validation_alpha'] = 'Fältet {field} kan bara innehålla alfanumeriska tecken.';
$lang['form_validation_alpha_numeric'] = 'Fältet {field} bör bara innehålla alfanumeriska tecken.';
$lang['form_validation_alpha_numeric_spaces'] = 'Fältet {field} bör bara innehålla alfanumeriska tecken och mellanslag.';
$lang['form_validation_alpha_dash'] = 'Fältet {field} kan bara innehålla alfanumeriska tecken, understryckningstecken och punkter.';
$lang['form_validation_numeric'] = 'Fältet {field} får bara innehålla siffror.';
$lang['form_validation_is_numeric'] = 'Fältet {field} får bara innehålla numeriska tecken.';
$lang['form_validation_integer'] = 'Fältet {field} får bara innehålla heltal.';
$lang['form_validation_regex_match'] = 'Fältet {field} har inte rätt format.';
$lang['form_validation_matches'] = 'Fältet {field} stämmer inte med fältet {param}.';
$lang['form_validation_differs'] = 'Fältet {field} måste vara annorlunda än fältet {param}.';
$lang['form_validation_is_unique'] = 'Fältet {field} måste innehålla ett unikt värde.';
$lang['form_validation_is_natural'] = 'Fältet {field} får bara innehålla siffror.';
$lang['form_validation_is_natural_no_zero'] = 'Fältet {field} får bara innehålla värden större än noll.';
$lang['form_validation_decimal'] = 'Fältet {field} måste innehålla ett decimaltal.';
$lang['form_validation_less_than'] = 'Fältet {field} måste innehåla ett nummer lägre än {param}.';
$lang['form_validation_less_than_equal_to'] = 'Fältet {field} måste innehålla ett nummer lika med eller lägre än {param}.';
$lang['form_validation_greater_than'] = 'Fältet {field} måste innehålla ett nummer större än {param}.';
$lang['form_validation_greater_than_equal_to'] = 'Fältet {field} måste innehålla ett nummer större än eller lika med {param}.';
$lang['form_validation_error_message_not_set'] = 'Kan inte hitta ett felmeddelande som hör till fältet {field}.';
$lang['form_validation_in_list'] = 'Fältet {field} måste vara en av: {param}.';
