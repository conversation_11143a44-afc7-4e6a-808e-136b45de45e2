<?php
/**
 * CodeIgniter
 *
 * An open source application development framework for PHP
 *
 * This content is released under the MIT License (MIT)
 *
 * Copyright (c) 2014 - 2019, British Columbia Institute of Technology
 *
 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to deal
 * in the Software without restriction, including without limitation the rights
 * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in
 * all copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
 * THE SOFTWARE.
 *
 * @package    CodeIgniter
 * <AUTHOR> Dev Team
 * @copyright    Copyright (c) 2008 - 2014, EllisLab, Inc. (https://ellislab.com/)
 * @copyright    Copyright (c) 2014 - 2019, British Columbia Institute of Technology (https://bcit.ca/)
 * @license    https://opensource.org/licenses/MIT	MIT License
 * @link    https://codeigniter.com
 * @since    Version 1.0.0
 * @filesource
 */
defined('BASEPATH') or exit('No direct script access allowed');

$lang['imglib_source_image_required'] = 'Você deve especificar uma imagem de origem em suas preferências.';
$lang['imglib_gd_required'] = 'A biblioteca de imagens GD é necessária para este recurso.';
$lang['imglib_gd_required_for_props'] = 'Seu servidor deve suportar a biblioteca de imagens GD para determinar as propriedades da imagem.';
$lang['imglib_unsupported_imagecreate'] = 'Seu servidor não suporta a função GD necessária para processar este tipo de imagem.';
$lang['imglib_gif_not_supported'] = 'Muitas vezes, as imagens GIF não são suportadas devido a restrições de licenciamento. Pode ser necessário usar imagens JPG ou PNG.';
$lang['imglib_jpg_not_supported'] = 'Imagens JPG não são compatíveis.';
$lang['imglib_png_not_supported'] = 'Imagens PNG não são suportadas.';
$lang['imglib_jpg_or_png_required'] = 'O protocolo de redimensionamento de imagem especificado em suas preferências funciona apenas com os tipos de imagem JPEG ou PNG.';
$lang['imglib_copy_error'] = 'Foi encontrado um erro ao tentar substituir o arquivo. Certifique-se de que seu diretório de arquivos seja gravável.';
$lang['imglib_rotate_unsupported'] = 'A rotação da imagem não parece ser compatível com o seu servidor.';
$lang['imglib_libpath_invalid'] = 'O caminho para sua biblioteca de imagens não está correto. Defina o caminho correto em suas preferências de imagem.';
$lang['imglib_image_process_failed'] = 'O processamento da imagem falhou. Verifique se o seu servidor suporta o protocolo escolhido e se o caminho para a sua biblioteca de imagens está correto.';
$lang['imglib_rotation_angle_required'] = 'É necessário um ângulo de rotação para girar a imagem.';
$lang['imglib_invalid_path'] = 'O caminho para a imagem não está correto.';
$lang['imglib_invalid_image'] = 'A imagem fornecida não é válida.';
$lang['imglib_copy_failed'] = 'A rotina de cópia da imagem falhou.';
$lang['imglib_missing_font'] = 'Incapaz de encontrar uma fonte para usar.';
$lang['imglib_save_failed'] = 'Não foi possível salvar a imagem. Certifique-se de que a imagem e o diretório do arquivo sejam graváveis.';
