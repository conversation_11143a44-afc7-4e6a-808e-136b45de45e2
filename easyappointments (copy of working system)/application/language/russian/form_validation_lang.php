<?php
/**
 * CodeIgniter
 *
 * An open source application development framework for PHP
 *
 * This content is released under the MIT License (MIT)
 *
 * Copyright (c) 2014 - 2019, British Columbia Institute of Technology
 *
 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to deal
 * in the Software without restriction, including without limitation the rights
 * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in
 * all copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
 * THE SOFTWARE.
 *
 * @package    CodeIgniter
 * <AUTHOR> Dev Team
 * @copyright    Copyright (c) 2008 - 2014, EllisLab, Inc. (https://ellislab.com/)
 * @copyright    Copyright (c) 2014 - 2019, British Columbia Institute of Technology (https://bcit.ca/)
 * @license    https://opensource.org/licenses/MIT	MIT License
 * @link    https://codeigniter.com
 * @since    Version 1.0.0
 * @filesource
 */
defined('BASEPATH') or exit('No direct script access allowed');

$lang['form_validation_required'] = 'Поле {field} является обязательным.';
$lang['form_validation_isset'] = 'Поле {field} должно иметь значение.';
$lang['form_validation_valid_email'] = 'Поле {field} должно содержать действительный адрес электронной почты.';
$lang['form_validation_valid_emails'] = 'Поле {field} должно содержать все действительные адреса электронной почты.';
$lang['form_validation_valid_url'] = 'Поле {field} должно содержать действительный URL.';
$lang['form_validation_valid_ip'] = 'Поле {field} должно содержать действительный IP.';
$lang['form_validation_valid_base64'] = 'Поле {field} должно содержать допустимую строку Base64.';
$lang['form_validation_min_length'] = 'Поле {field} должно быть длиной не менее {param} символов.';
$lang['form_validation_max_length'] = 'Длина поля {field} не может превышать {param} символов.';
$lang['form_validation_exact_length'] = 'Поле {field} должно быть длиной ровно {param} символов.';
$lang['form_validation_alpha'] = 'Поле {field} может содержать только алфавитные символы.';
$lang['form_validation_alpha_numeric'] = 'Поле {field} может содержать только буквенно-цифровые символы.';
$lang['form_validation_alpha_numeric_spaces'] = 'Поле {field} может содержать только буквенно-цифровые символы и пробелы.';
$lang['form_validation_alpha_dash'] = 'Поле {field} может содержать только буквенно-цифровые символы, подчеркивания и тире.';
$lang['form_validation_numeric'] = 'Поле {field} должно содержать только числа.';
$lang['form_validation_is_numeric'] = 'Поле {field} должно содержать только числовые символы.';
$lang['form_validation_integer'] = 'Поле {field} должно содержать целое число.';
$lang['form_validation_regex_match'] = 'Поле {field} имеет неправильный формат.';
$lang['form_validation_matches'] = 'Поле {field} не соответствует полю {param}.';
$lang['form_validation_differs'] = 'Поле {field} должно отличаться от поля {param}.';
$lang['form_validation_is_unique'] = 'Поле {field} должно содержать уникальное значение.';
$lang['form_validation_is_natural'] = 'Поле {field} должно содержать только цифры.';
$lang['form_validation_is_natural_no_zero'] = 'Поле {field} должно содержать только цифры и должно быть больше нуля.';
$lang['form_validation_decimal'] = 'Поле {field} должно содержать десятичное число.';
$lang['form_validation_less_than'] = 'Поле {field} должно содержать число, меньшее, чем {param}.';
$lang['form_validation_less_than_equal_to'] = 'Поле {field} должно содержать число, меньшее или равное {param}.';
$lang['form_validation_greater_than'] = 'Поле {field} должно содержать число, большее, чем {param}.';
$lang['form_validation_greater_than_equal_to'] = 'Поле {field} должно содержать число, большее или равное {param}.';
$lang['form_validation_error_message_not_set'] = 'Невозможно получить доступ к сообщению об ошибке, соответствующему имени вашего поля {field}.';
$lang['form_validation_in_list'] = 'Поле {field} должно быть одним из: {param}.';
