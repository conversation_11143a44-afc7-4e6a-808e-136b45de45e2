<?php
/**
 * CodeIgniter
 *
 * An open source application development framework for PHP
 *
 * This content is released under the MIT License (MIT)
 *
 * Copyright (c) 2014 - 2019, British Columbia Institute of Technology
 *
 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to deal
 * in the Software without restriction, including without limitation the rights
 * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in
 * all copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
 * THE SOFTWARE.
 *
 * @package    CodeIgniter
 * <AUTHOR> Dev Team
 * @copyright    Copyright (c) 2008 - 2014, EllisLab, Inc. (https://ellislab.com/)
 * @copyright    Copyright (c) 2014 - 2019, British Columbia Institute of Technology (https://bcit.ca/)
 * @license    https://opensource.org/licenses/MIT	MIT License
 * @link    https://codeigniter.com
 * @since    Version 1.0.0
 * @filesource
 */
defined('BASEPATH') or exit('No direct script access allowed');

$lang['upload_userfile_not_set'] = 'Невозможно найти запрошенную переменную с именем userfile.';
$lang['upload_file_exceeds_limit'] = 'Загруженный файл превышает максимально допустимый размер в вашем конфигурационном файле PHP.';
$lang['upload_file_exceeds_form_limit'] = 'Загруженный файл превышает максимальный размер, разрешенный формой отправки.';
$lang['upload_file_partial'] = 'Файл был загружен только частично.';
$lang['upload_no_temp_directory'] = 'Временная папка отсутствует.';
$lang['upload_unable_to_write_file'] = 'Файл не может быть записан на диск.';
$lang['upload_stopped_by_extension'] = 'Загрузка файла была остановлена расширением.';
$lang['upload_no_file_selected'] = 'Вы не выбрали файл для загрузки.';
$lang['upload_invalid_filetype'] = 'Тип файла, который вы пытаетесь загрузить, не разрешен.';
$lang['upload_invalid_filesize'] = 'Размер файла, который вы пытаетесь загрузить, превышает допустимый.';
$lang['upload_invalid_dimensions'] = 'Изображение, которое вы пытаетесь загрузить, не соответствует допустимым размерам.';
$lang['upload_destination_error'] = 'При попытке переместить загруженный файл в конечный пункт назначения возникла проблема.';
$lang['upload_no_filepath'] = 'Путь загрузки не является действительным.';
$lang['upload_no_file_types'] = 'Вы не указали ни одного разрешенного типа файлов.';
$lang['upload_bad_filename'] = 'Имя файла, которое вы предоставили, уже существует на сервере.';
$lang['upload_not_writable'] = 'Похоже, что папка назначения загрузки не доступна для записи.';
