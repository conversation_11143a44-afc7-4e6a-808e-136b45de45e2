<?php
/**
 * System messages translation for CodeIgniter(tm)
 *
 * <AUTHOR> community
 * @copyright    Copyright (c) 2014-2018, British Columbia Institute of Technology (http://bcit.ca/)
 * @license    http://opensource.org/licenses/MIT	MIT License
 * @link    https://codeigniter.com
 */
defined('BASEPATH') or exit('No direct script access allowed');

$lang['migration_none_found'] = '找不到任何遷移。';
$lang['migration_not_found'] = '找不到版本號為 %s 的遷移。';
$lang['migration_sequence_gap'] = '在版本號 %s 附近的遷移序列中存在落差。';
$lang['migration_multiple_version'] = '存在多個具有相同版本號的遷移：%s。';
$lang['migration_class_doesnt_exist'] = '遷移類別 "%s" 無法找到。';
$lang['migration_missing_up_method'] = '遷移類別 "%s" 缺少 "up" 方法。';
$lang['migration_missing_down_method'] = '遷移類別 "%s" 缺少 "down" 方法。';
$lang['migration_invalid_filename'] = '遷移 "%s" 的檔案名稱無效。';
