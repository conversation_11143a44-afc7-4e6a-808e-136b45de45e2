<?php
/**
 * CodeIgniter
 *
 * An open source application development framework for PHP
 *
 * This content is released under the MIT License (MIT)
 *
 * Copyright (c) 2014 - 2019, British Columbia Institute of Technology
 *
 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to deal
 * in the Software without restriction, including without limitation the rights
 * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in
 * all copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
 * THE SOFTWARE.
 *
 * @package    CodeIgniter
 * <AUTHOR> Dev Team
 * @copyright    Copyright (c) 2008 - 2014, EllisLab, Inc. (https://ellislab.com/)
 * @copyright    Copyright (c) 2014 - 2019, British Columbia Institute of Technology (https://bcit.ca/)
 * @license    https://opensource.org/licenses/MIT	MIT License
 * @link    https://codeigniter.com
 * @since    Version 1.0.0
 * @filesource
 */
defined('BASEPATH') or exit('No direct script access allowed');

$lang['ftp_no_connection'] = '無法找到有效的連線 ID。請確保您已連線後再執行任何檔案作業。';
$lang['ftp_unable_to_connect'] = '無法使用提供的主機名稱連線到您的 FTP 伺服器。';
$lang['ftp_unable_to_login'] = '無法登入您的 FTP 伺服器。請檢查您的使用者名稱和密碼。';
$lang['ftp_unable_to_mkdir'] = '無法建立您指定的目錄。';
$lang['ftp_unable_to_changedir'] = '無法切換目錄。';
$lang['ftp_unable_to_chmod'] = '無法設定檔案權限。請檢查您的路徑。';
$lang['ftp_unable_to_upload'] = '無法上傳指定的檔案。請檢查您的路徑。';
$lang['ftp_unable_to_download'] = '無法下載指定的檔案。請檢查您的路徑。';
$lang['ftp_no_source_file'] = '無法找到來源檔案。請檢查您的路徑。';
$lang['ftp_unable_to_rename'] = '無法重新命名檔案。';
$lang['ftp_unable_to_delete'] = '無法刪除檔案。';
$lang['ftp_unable_to_move'] = '無法移動檔案。請確保目的地目錄存在。';
