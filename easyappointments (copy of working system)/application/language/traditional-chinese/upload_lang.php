<?php
/**
 * CodeIgniter
 *
 * An open source application development framework for PHP
 *
 * This content is released under the MIT License (MIT)
 *
 * Copyright (c) 2014 - 2019, British Columbia Institute of Technology
 *
 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to deal
 * in the Software without restriction, including without limitation the rights
 * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in
 * all copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
 * THE SOFTWARE.
 *
 * @package    CodeIgniter
 * <AUTHOR> Dev Team
 * @copyright    Copyright (c) 2008 - 2014, EllisLab, Inc. (https://ellislab.com/)
 * @copyright    Copyright (c) 2014 - 2019, British Columbia Institute of Technology (https://bcit.ca/)
 * @license    https://opensource.org/licenses/MIT	MIT License
 * @link    https://codeigniter.com
 * @since    Version 1.0.0
 * @filesource
 */
defined('BASEPATH') or exit('No direct script access allowed');

$lang['upload_userfile_not_set'] = '無法找到名為 userfile 的 POST 變數。';
$lang['upload_file_exceeds_limit'] = '上傳的檔案超過了 PHP 設定檔中允許的最大大小。';
$lang['upload_file_exceeds_form_limit'] = '上傳的檔案超過了提交表單允許的最大大小。';
$lang['upload_file_partial'] = '檔案僅部分上傳。';
$lang['upload_no_temp_directory'] = '找不到暫存資料夾。';
$lang['upload_unable_to_write_file'] = '無法將檔案寫入磁碟。';
$lang['upload_stopped_by_extension'] = '檔案上傳被擴充功能阻止。';
$lang['upload_no_file_selected'] = '您尚未選擇要上傳的檔案。';
$lang['upload_invalid_filetype'] = '您嘗試上傳的檔案類型不被允許。';
$lang['upload_invalid_filesize'] = '您嘗試上傳的檔案大小超過允許範圍。';
$lang['upload_invalid_dimensions'] = '您嘗試上傳的圖片尺寸不符合允許的範圍。';
$lang['upload_destination_error'] = '嘗試將上傳的檔案移動到最終目的地時發生問題。';
$lang['upload_no_filepath'] = '上傳路徑似乎無效。';
$lang['upload_no_file_types'] = '您尚未指定任何允許的檔案類型。';
$lang['upload_bad_filename'] = '您提交的檔案名稱已存在於伺服器上。';
$lang['upload_not_writable'] = '上傳目的地資料夾似乎無法寫入。';
