<?php
/**
 * CodeIgniter
 *
 * An open source application development framework for PHP
 *
 * This content is released under the MIT License (MIT)
 *
 * Copyright (c) 2014 - 2019, British Columbia Institute of Technology
 *
 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to deal
 * in the Software without restriction, including without limitation the rights
 * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in
 * all copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
 * THE SOFTWARE.
 *
 * @package    CodeIgniter
 * <AUTHOR> Dev Team
 * @copyright    Copyright (c) 2008 - 2014, EllisLab, Inc. (https://ellislab.com/)
 * @copyright    Copyright (c) 2014 - 2019, British Columbia Institute of Technology (https://bcit.ca/)
 * @license    https://opensource.org/licenses/MIT	MIT License
 * @link    https://codeigniter.com
 * @since    Version 1.0.0
 * @filesource
 */
defined('BASEPATH') or exit('No direct script access allowed');

$lang['form_validation_required'] = '{field} 欄位為必填';
$lang['form_validation_isset'] = '{field} 欄位必須有值';
$lang['form_validation_valid_email'] = '{field} 欄位必須是有效的電子郵件地址';
$lang['form_validation_valid_emails'] = '{field} 欄位必須包含所有有效的電子郵件地址';
$lang['form_validation_valid_url'] = '{field} 欄位必須是有效的網址';
$lang['form_validation_valid_ip'] = '{field} 欄位必須是有效的 IP 位址';
$lang['form_validation_valid_base64'] = '{field} 欄位必須是有效的 Base64 字串';
$lang['form_validation_min_length'] = '{field} 欄位長度至少需要 {param} 個字元';
$lang['form_validation_max_length'] = '{field} 欄位長度不能超過 {param} 個字元';
$lang['form_validation_exact_length'] = '{field} 欄位長度必須剛好是 {param} 個字元';
$lang['form_validation_alpha'] = '{field} 欄位僅能包含英文字母';
$lang['form_validation_alpha_numeric'] = '{field} 欄位僅能包含英文字母與數字';
$lang['form_validation_alpha_numeric_spaces'] = '{field} 欄位僅能包含英文字母、數字和空格';
$lang['form_validation_alpha_dash'] = '{field} 欄位僅能包含英文字母、數字、底線和破折號';
$lang['form_validation_numeric'] = '{field} 欄位僅能包含數字';
$lang['form_validation_is_numeric'] = '{field} 欄位僅能包含數字字元';
$lang['form_validation_integer'] = '{field} 欄位必須是整數';
$lang['form_validation_regex_match'] = '{field} 欄位格式不正確';
$lang['form_validation_matches'] = '{field} 欄位與 {param} 欄位不相符';
$lang['form_validation_differs'] = '{field} 欄位必須與 {param} 欄位不同';
$lang['form_validation_is_unique'] = '{field} 欄位必須是唯一值';
$lang['form_validation_is_natural'] = '{field} 欄位僅能包含數字';
$lang['form_validation_is_natural_no_zero'] = '{field} 欄位僅能包含數字且必須大於零';
$lang['form_validation_decimal'] = '{field} 欄位必須是十進位數字';
$lang['form_validation_less_than'] = '{field} 欄位必須小於 {param}';
$lang['form_validation_less_than_equal_to'] = '{field} 欄位必須小於或等於 {param}';
$lang['form_validation_greater_than'] = '{field} 欄位必須大於 {param}';
$lang['form_validation_greater_than_equal_to'] = '{field} 欄位必須大於或等於 {param}';
$lang['form_validation_error_message_not_set'] = '無法存取與您的欄位名稱 {field} 相對應的錯誤訊息';
$lang['form_validation_in_list'] = '{field} 欄位必須是下列其中之一：{param}';
