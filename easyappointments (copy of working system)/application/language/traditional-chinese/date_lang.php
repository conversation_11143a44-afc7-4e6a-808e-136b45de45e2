<?php
/**
 * CodeIgniter
 *
 * An open source application development framework for PHP
 *
 * This content is released under the MIT License (MIT)
 *
 * Copyright (c) 2014 - 2019, British Columbia Institute of Technology
 *
 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to deal
 * in the Software without restriction, including without limitation the rights
 * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in
 * all copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
 * THE SOFTWARE.
 *
 * @package    CodeIgniter
 * <AUTHOR> Dev Team
 * @copyright    Copyright (c) 2008 - 2014, EllisLab, Inc. (https://ellislab.com/)
 * @copyright    Copyright (c) 2014 - 2019, British Columbia Institute of Technology (https://bcit.ca/)
 * @license    https://opensource.org/licenses/MIT	MIT License
 * @link    https://codeigniter.com
 * @since    Version 1.0.0
 * @filesource
 */
defined('BASEPATH') or exit('No direct script access allowed');

$lang['date_year'] = '年';
$lang['date_years'] = '年';
$lang['date_month'] = '月';
$lang['date_months'] = '個月';
$lang['date_week'] = '週';
$lang['date_weeks'] = '週';
$lang['date_day'] = '天';
$lang['date_days'] = '天';
$lang['date_hour'] = '小時';
$lang['date_hours'] = '小時';
$lang['date_minute'] = '分鐘';
$lang['date_minutes'] = '分鐘';
$lang['date_second'] = '秒';
$lang['date_seconds'] = '秒';

$lang['UM12'] = '(UTC -12:00) 貝克島／豪蘭島';
$lang['UM11'] = '(UTC -11:00) 紐埃';
$lang['UM10'] = '(UTC -10:00) 夏威夷－阿留申標準時間，庫克群島，大溪地';
$lang['UM95'] = '(UTC -9:30) 馬克薩斯群島';
$lang['UM9'] = '(UTC -9:00) 阿拉斯加標準時間，甘比爾群島';
$lang['UM8'] = '(UTC -8:00) 太平洋標準時間，克利珀頓島';
$lang['UM7'] = '(UTC -7:00) 山區標準時間';
$lang['UM6'] = '(UTC -6:00) 中部標準時間';
$lang['UM5'] = '(UTC -5:00) 東部標準時間，西加勒比海標準時間';
$lang['UM45'] = '(UTC -4:30) 委內瑞拉標準時間';
$lang['UM4'] = '(UTC -4:00) 大西洋標準時間，東加勒比海標準時間';
$lang['UM35'] = '(UTC -3:30) 紐芬蘭標準時間';
$lang['UM3'] = '(UTC -3:00) 阿根廷，巴西，法屬圭亞那，烏拉圭';
$lang['UM2'] = '(UTC -2:00) 南喬治亞島／南桑威奇群島';
$lang['UM1'] = '(UTC -1:00) 亞速爾群島，維德角群島';
$lang['UTC'] = '(UTC) 格林威治標準時間，西歐時間';
$lang['UP1'] = '(UTC +1:00) 中歐時間，西非時間';
$lang['UP2'] = '(UTC +2:00) 中非時間，東歐時間，加里寧格勒時間';
$lang['UP3'] = '(UTC +3:00) 莫斯科時間，東非時間，阿拉伯標準時間';
$lang['UP35'] = '(UTC +3:30) 伊朗標準時間';
$lang['UP4'] = '(UTC +4:00) 亞塞拜然標準時間，薩馬拉時間';
$lang['UP45'] = '(UTC +4:30) 阿富汗';
$lang['UP5'] = '(UTC +5:00) 巴基斯坦標準時間，葉卡捷琳堡時間';
$lang['UP55'] = '(UTC +5:30) 印度標準時間，斯里蘭卡時間';
$lang['UP575'] = '(UTC +5:45) 尼泊爾時間';
$lang['UP6'] = '(UTC +6:00) 孟加拉標準時間，不丹時間，鄂木斯克時間';
$lang['UP65'] = '(UTC +6:30) 科科斯群島，緬甸';
$lang['UP7'] = '(UTC +7:00) 克拉斯諾亞爾斯克時間，柬埔寨，寮國，泰國，越南';
$lang['UP8'] = '(UTC +8:00) 澳洲西部標準時間，北京時間，伊爾庫次克時間';
$lang['UP875'] = '(UTC +8:45) 澳洲中西部標準時間';
$lang['UP9'] = '(UTC +9:00) 日本標準時間，韓國標準時間，雅庫茨克時間';
$lang['UP95'] = '(UTC +9:30) 澳洲中部標準時間';
$lang['UP10'] = '(UTC +10:00) 澳洲東部標準時間，海參崴時間';
$lang['UP105'] = '(UTC +10:30) 豪勳爵島';
$lang['UP11'] = '(UTC +11:00) 中科雷姆斯克時間，所羅門群島，萬那杜';
$lang['UP115'] = '(UTC +11:30) 諾福克島';
$lang['UP12'] = '(UTC +12:00) 斐濟，吉爾伯特群島，堪察加時間，紐西蘭標準時間';
$lang['UP1275'] = '(UTC +12:45) 查塔姆群島標準時間';
$lang['UP13'] = '(UTC +13:00) 薩摩亞時區，鳳凰群島時間，東加';
$lang['UP14'] = '(UTC +14:00) 萊恩群島';
