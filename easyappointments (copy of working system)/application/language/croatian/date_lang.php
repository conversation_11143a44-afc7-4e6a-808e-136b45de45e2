<?php
/**
 * CodeIgniter
 *
 * An open source application development framework for PHP
 *
 * This content is released under the MIT License (MIT)
 *
 * Copyright (c) 2014 - 2019, British Columbia Institute of Technology
 *
 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to deal
 * in the Software without restriction, including without limitation the rights
 * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in
 * all copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
 * THE SOFTWARE.
 *
 * @package    CodeIgniter
 * <AUTHOR> Dev Team
 * @copyright    Copyright (c) 2008 - 2014, EllisLab, Inc. (https://ellislab.com/)
 * @copyright    Copyright (c) 2014 - 2019, British Columbia Institute of Technology (https://bcit.ca/)
 * @license    https://opensource.org/licenses/MIT	MIT License
 * @link    https://codeigniter.com
 * @since    Version 1.0.0
 * @filesource
 */
defined('BASEPATH') or exit('No direct script access allowed');

$lang['date_year'] = 'Godina';
$lang['date_years'] = 'Godine';
$lang['date_month'] = 'Mjesec';
$lang['date_months'] = 'Mjeseci';
$lang['date_week'] = 'Tjedan';
$lang['date_weeks'] = 'Tjedni';
$lang['date_day'] = 'Dan';
$lang['date_days'] = 'Dani';
$lang['date_hour'] = 'Sat';
$lang['date_hours'] = 'Sati';
$lang['date_minute'] = 'Minuta';
$lang['date_minutes'] = 'Minute';
$lang['date_second'] = 'Sekunda';
$lang['date_seconds'] = 'Sekunde';

$lang['UM12'] = '(UTC -12:00) Baker/Howland otok';
$lang['UM11'] = '(UTC -11:00) Niue';
$lang['UM10'] = '(UTC -10:00) Havajsko-aleućansko standardno vrijeme, Cookovi otoci, Tahiti';
$lang['UM95'] = '(UTC -9:30) Marquesas otoci';
$lang['UM9'] = '(UTC -9:00) Aljaško standardno vrijeme, Gambier otoci';
$lang['UM8'] = '(UTC -8:00) Pacifičko standardno vrijeme, Clipperton otok';
$lang['UM7'] = '(UTC -7:00) Planinsko standardno vrijeme';
$lang['UM6'] = '(UTC -6:00) Središnje standardno vrijeme';
$lang['UM5'] = '(UTC -5:00) Istočno standardno vrijeme, Zapadno-karipsko standardno vrijeme';
$lang['UM45'] = '(UTC -4:30) Venezuelansko standardno vrijeme';
$lang['UM4'] = '(UTC -4:00) Atlantsko standardno vrijeme, Istočno-karipsko standardno vrijeme';
$lang['UM35'] = '(UTC -3:30) Newfoundlandsko standardno vrijeme';
$lang['UM3'] = '(UTC -3:00) Argentina, Brazil, Francuska Gvajana, Urugvaj';
$lang['UM2'] = '(UTC -2:00) Južna Gruzija/Južni Sandwich otoci';
$lang['UM1'] = '(UTC -1:00) Azori, Zelenortski otoci';
$lang['UTC'] = '(UTC) Greenwichko srednje vrijeme, Zapadnoeuropsko vrijeme';
$lang['UP1'] = '(UTC +1:00) Srednjoeuropsko vrijeme, Zapadnoafričko vrijeme';
$lang['UP2'] = '(UTC +2:00) Srednjoafričko vrijeme, Istočnoeuropsko vrijeme, Kalinjingradsko vrijeme';
$lang['UP3'] = '(UTC +3:00) Moskovsko vrijeme, Istočnoafričko vrijeme, Arabsko standardno vrijeme';
$lang['UP35'] = '(UTC +3:30) Iransko standardno vrijeme';
$lang['UP4'] = '(UTC +4:00) Azerbajdžansko standardno vrijeme, Samarsko vrijeme';
$lang['UP45'] = '(UTC +4:30) Afganistan';
$lang['UP5'] = '(UTC +5:00) Pakistansko standardno vrijeme, Jekaterinburško vrijeme';
$lang['UP55'] = '(UTC +5:30) Indijsko standardno vrijeme, Šrilankansko vrijeme';
$lang['UP575'] = '(UTC +5:45) Vrijeme u Nepalu';
$lang['UP6'] = '(UTC +6:00) Bangladeško standardno vrijeme, Butansko vrijeme, Omsko vrijeme';
$lang['UP65'] = '(UTC +6:30) Kokosovi otoci, Mijanmar';
$lang['UP7'] = '(UTC +7:00) Krasnojarsko vrijeme, Kambodža, Laos, Tajland, Vijetnam';
$lang['UP8'] = '(UTC +8:00) Australijsko zapadno standardno vrijeme, Pekinško vrijeme, Irkutsko vrijeme';
$lang['UP875'] = '(UTC +8:45) Australijsko središnje zapadno standardno vrijeme';
$lang['UP9'] = '(UTC +9:00) Japansko standardno vrijeme, Korejsko standardno vrijeme, Jakutsko vrijeme';
$lang['UP95'] = '(UTC +9:30) Australijsko središnje standardno vrijeme';
$lang['UP10'] = '(UTC +10:00) Australijsko istočno standardno vrijeme, Vladivostočko vrijeme';
$lang['UP105'] = '(UTC +10:30) Otok Lord Howe';
$lang['UP11'] = '(UTC +11:00) Srednekolymsko vrijeme, Salomonovi otoci, Vanuatu';
$lang['UP115'] = '(UTC +11:30) Otok Norfolk';
$lang['UP12'] = '(UTC +12:00) Fidži, Gilbertovi otoci, Kamčatsko vrijeme, Novozelandsko standardno vrijeme';
$lang['UP1275'] = '(UTC +12:45) Standardno vrijeme na Chathamskim otocima';
$lang['UP13'] = '(UTC +13:00) Vremenska zona Samoe, Phoenix otoci, Tonga';
$lang['UP14'] = '(UTC +14:00) Line otoci';
