<?php
/**
 * CodeIgniter
 *
 * An open source application development framework for PHP
 *
 * This content is released under the MIT License (MIT)
 *
 * Copyright (c) 2014 - 2019, British Columbia Institute of Technology
 *
 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to deal
 * in the Software without restriction, including without limitation the rights
 * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in
 * all copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
 * THE SOFTWARE.
 *
 * @package    CodeIgniter
 * <AUTHOR> Dev Team
 * @copyright    Copyright (c) 2008 - 2014, EllisLab, Inc. (https://ellislab.com/)
 * @copyright    Copyright (c) 2014 - 2019, British Columbia Institute of Technology (https://bcit.ca/)
 * @license    https://opensource.org/licenses/MIT	MIT License
 * @link    https://codeigniter.com
 * @since    Version 1.0.0
 * @filesource
 */
defined('BASEPATH') or exit('No direct script access allowed');

$lang['cal_su'] = 'Ne';
$lang['cal_mo'] = 'Po';
$lang['cal_tu'] = 'Ut';
$lang['cal_we'] = 'Sr';
$lang['cal_th'] = 'Če';
$lang['cal_fr'] = 'Pe';
$lang['cal_sa'] = 'Su';
$lang['cal_sun'] = 'Ned';
$lang['cal_mon'] = 'Pon';
$lang['cal_tue'] = 'Uto';
$lang['cal_wed'] = 'Sri';
$lang['cal_thu'] = 'Čet';
$lang['cal_fri'] = 'Pet';
$lang['cal_sat'] = 'Sub';
$lang['cal_sunday'] = 'Nedjelja';
$lang['cal_monday'] = 'Ponedjeljak';
$lang['cal_tuesday'] = 'Utorak';
$lang['cal_wednesday'] = 'Srijeda';
$lang['cal_thursday'] = 'Četvrtak';
$lang['cal_friday'] = 'Petak';
$lang['cal_saturday'] = 'Subota';
$lang['cal_jan'] = 'Sij';
$lang['cal_feb'] = 'Vel';
$lang['cal_mar'] = 'Ožu';
$lang['cal_apr'] = 'Tra';
$lang['cal_may'] = 'Svi';
$lang['cal_jun'] = 'Lip';
$lang['cal_jul'] = 'Srp';
$lang['cal_aug'] = 'Kol';
$lang['cal_sep'] = 'Ruj';
$lang['cal_oct'] = 'Lis';
$lang['cal_nov'] = 'Stu';
$lang['cal_dec'] = 'Pro';
$lang['cal_january'] = 'Siječanj';
$lang['cal_february'] = 'Veljača';
$lang['cal_march'] = 'Ožujak';
$lang['cal_april'] = 'Travanj';
$lang['cal_mayl'] = 'Svibanj';
$lang['cal_june'] = 'Lipanj';
$lang['cal_july'] = 'Srpanj';
$lang['cal_august'] = 'Kolovoz';
$lang['cal_september'] = 'Rujan';
$lang['cal_october'] = 'Listopad';
$lang['cal_november'] = 'Studeni';
$lang['cal_december'] = 'Prosinac';
