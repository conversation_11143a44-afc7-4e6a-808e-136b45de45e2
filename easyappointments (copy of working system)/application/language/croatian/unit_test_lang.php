<?php
/**
 * CodeIgniter
 *
 * An open source application development framework for PHP
 *
 * This content is released under the MIT License (MIT)
 *
 * Copyright (c) 2014 - 2019, British Columbia Institute of Technology
 *
 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to deal
 * in the Software without restriction, including without limitation the rights
 * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in
 * all copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
 * THE SOFTWARE.
 *
 * @package    CodeIgniter
 * <AUTHOR> Dev Team
 * @copyright    Copyright (c) 2008 - 2014, EllisLab, Inc. (https://ellislab.com/)
 * @copyright    Copyright (c) 2014 - 2019, British Columbia Institute of Technology (https://bcit.ca/)
 * @license    https://opensource.org/licenses/MIT	MIT License
 * @link    https://codeigniter.com
 * @since    Version 1.0.0
 * @filesource
 */
defined('BASEPATH') or exit('No direct script access allowed');

$lang['ut_test_name'] = 'Naziv Testa';
$lang['ut_test_datatype'] = 'Tip Podataka Testa';
$lang['ut_res_datatype'] = 'Očekivani Tip Podataka';
$lang['ut_result'] = 'Rezultat';
$lang['ut_undefined'] = 'Nedefinirani Naziv Testa';
$lang['ut_file'] = 'Naziv Datoteke';
$lang['ut_line'] = 'Broj Linije';
$lang['ut_passed'] = 'Prošao';
$lang['ut_failed'] = 'Nije Prošao';
$lang['ut_boolean'] = 'Booleova Vrijednost';
$lang['ut_integer'] = 'Cijeli Broj';
$lang['ut_float'] = 'Decimalni Broj';
$lang['ut_double'] = 'Decimalni Broj'; // can be the same as float
$lang['ut_string'] = 'Niz Znakova';
$lang['ut_array'] = 'Polje';
$lang['ut_object'] = 'Objekt';
$lang['ut_resource'] = 'Resurs';
$lang['ut_null'] = 'Nula';
$lang['ut_notes'] = 'Bilješke';
