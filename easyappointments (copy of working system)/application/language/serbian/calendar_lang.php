<?php
/**
 * CodeIgniter
 *
 * An open source application development framework for PHP
 *
 * This content is released under the MIT License (MIT)
 *
 * Copyright (c) 2014 - 2019, British Columbia Institute of Technology
 *
 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to deal
 * in the Software without restriction, including without limitation the rights
 * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in
 * all copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
 * THE SOFTWARE.
 *
 * @package    CodeIgniter
 * <AUTHOR> Dev Team
 * @copyright    Copyright (c) 2008 - 2014, EllisLab, Inc. (https://ellislab.com/)
 * @copyright    Copyright (c) 2014 - 2019, British Columbia Institute of Technology (https://bcit.ca/)
 * @license    https://opensource.org/licenses/MIT	MIT License
 * @link    https://codeigniter.com
 * @since    Version 1.0.0
 * @filesource
 */
defined('BASEPATH') or exit('No direct script access allowed');

$lang['cal_su'] = 'Su';
$lang['cal_mo'] = 'Mo';
$lang['cal_tu'] = 'Tu';
$lang['cal_we'] = 'We';
$lang['cal_th'] = 'Th';
$lang['cal_fr'] = 'Fr';
$lang['cal_sa'] = 'Sa';
$lang['cal_sun'] = 'Sun';
$lang['cal_mon'] = 'Mon';
$lang['cal_tue'] = 'Tue';
$lang['cal_wed'] = 'Wed';
$lang['cal_thu'] = 'Thu';
$lang['cal_fri'] = 'Fri';
$lang['cal_sat'] = 'Sat';
$lang['cal_sunday'] = 'Sunday';
$lang['cal_monday'] = 'Monday';
$lang['cal_tuesday'] = 'Tuesday';
$lang['cal_wednesday'] = 'Wednesday';
$lang['cal_thursday'] = 'Thursday';
$lang['cal_friday'] = 'Friday';
$lang['cal_saturday'] = 'Saturday';
$lang['cal_jan'] = 'Jan';
$lang['cal_feb'] = 'Feb';
$lang['cal_mar'] = 'Mar';
$lang['cal_apr'] = 'Apr';
$lang['cal_may'] = 'May';
$lang['cal_jun'] = 'Jun';
$lang['cal_jul'] = 'Jul';
$lang['cal_aug'] = 'Aug';
$lang['cal_sep'] = 'Sep';
$lang['cal_oct'] = 'Oct';
$lang['cal_nov'] = 'Nov';
$lang['cal_dec'] = 'Dec';
$lang['cal_january'] = 'January';
$lang['cal_february'] = 'February';
$lang['cal_march'] = 'March';
$lang['cal_april'] = 'April';
$lang['cal_mayl'] = 'May';
$lang['cal_june'] = 'June';
$lang['cal_july'] = 'July';
$lang['cal_august'] = 'August';
$lang['cal_september'] = 'September';
$lang['cal_october'] = 'October';
$lang['cal_november'] = 'November';
$lang['cal_december'] = 'December';
