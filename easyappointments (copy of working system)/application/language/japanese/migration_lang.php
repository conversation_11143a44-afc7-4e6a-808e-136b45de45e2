<?php
/**
 * System messages translation for CodeIgniter(tm)
 *
 * <AUTHOR> community
 * @copyright    Copyright (c) 2014-2018, British Columbia Institute of Technology (http://bcit.ca/)
 * @license    http://opensource.org/licenses/MIT	MIT License
 * @link    https://codeigniter.com
 */
defined('BASEPATH') or exit('No direct script access allowed');

$lang['migration_none_found'] = 'マイグレーション対象は見つかりませんでした';
$lang['migration_not_found'] = '%sのバージョンではマイグレーション対象は見つかりませんでした';
$lang['migration_sequence_gap'] = '%sのバージョンではマイグレーション順序にギャップがあります';
$lang['migration_multiple_version'] = '複数のマイグレーションが%sのバージョンであります';
$lang['migration_class_doesnt_exist'] = 'マイグレーションクラス "%s"が見つかりません';
$lang['migration_missing_up_method'] = '"up"メソッドではマイグレーションクラス "%s" は見つかりません';
$lang['migration_missing_down_method'] = '"down"メソッドではマイグレーションクラス "%s" は見つかりません';
$lang['migration_invalid_filename'] = 'マイグレーション "%s" に不正なファイル名があります';
