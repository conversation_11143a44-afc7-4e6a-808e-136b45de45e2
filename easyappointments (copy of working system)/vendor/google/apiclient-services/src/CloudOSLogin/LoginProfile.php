<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service\CloudOSLogin;

class LoginProfile extends \Google\Collection
{
  protected $collection_key = 'posixAccounts';
  /**
   * @var string
   */
  public $name;
  protected $posixAccountsType = PosixAccount::class;
  protected $posixAccountsDataType = 'array';
  protected $sshPublicKeysType = SshPublicKey::class;
  protected $sshPublicKeysDataType = 'map';

  /**
   * @param string
   */
  public function setName($name)
  {
    $this->name = $name;
  }
  /**
   * @return string
   */
  public function getName()
  {
    return $this->name;
  }
  /**
   * @param PosixAccount[]
   */
  public function setPosixAccounts($posixAccounts)
  {
    $this->posixAccounts = $posixAccounts;
  }
  /**
   * @return PosixAccount[]
   */
  public function getPosixAccounts()
  {
    return $this->posixAccounts;
  }
  /**
   * @param SshPublicKey[]
   */
  public function setSshPublicKeys($sshPublicKeys)
  {
    $this->sshPublicKeys = $sshPublicKeys;
  }
  /**
   * @return SshPublicKey[]
   */
  public function getSshPublicKeys()
  {
    return $this->sshPublicKeys;
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(LoginProfile::class, 'Google_Service_CloudOSLogin_LoginProfile');
