<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service\Compute;

class ResourcePolicySnapshotSchedulePolicySchedule extends \Google\Model
{
  protected $dailyScheduleType = ResourcePolicyDailyCycle::class;
  protected $dailyScheduleDataType = '';
  protected $hourlyScheduleType = ResourcePolicyHourlyCycle::class;
  protected $hourlyScheduleDataType = '';
  protected $weeklyScheduleType = ResourcePolicyWeeklyCycle::class;
  protected $weeklyScheduleDataType = '';

  /**
   * @param ResourcePolicyDailyCycle
   */
  public function setDailySchedule(ResourcePolicyDailyCycle $dailySchedule)
  {
    $this->dailySchedule = $dailySchedule;
  }
  /**
   * @return ResourcePolicyDailyCycle
   */
  public function getDailySchedule()
  {
    return $this->dailySchedule;
  }
  /**
   * @param ResourcePolicyHourlyCycle
   */
  public function setHourlySchedule(ResourcePolicyHourlyCycle $hourlySchedule)
  {
    $this->hourlySchedule = $hourlySchedule;
  }
  /**
   * @return ResourcePolicyHourlyCycle
   */
  public function getHourlySchedule()
  {
    return $this->hourlySchedule;
  }
  /**
   * @param ResourcePolicyWeeklyCycle
   */
  public function setWeeklySchedule(ResourcePolicyWeeklyCycle $weeklySchedule)
  {
    $this->weeklySchedule = $weeklySchedule;
  }
  /**
   * @return ResourcePolicyWeeklyCycle
   */
  public function getWeeklySchedule()
  {
    return $this->weeklySchedule;
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(ResourcePolicySnapshotSchedulePolicySchedule::class, 'Google_Service_Compute_ResourcePolicySnapshotSchedulePolicySchedule');
