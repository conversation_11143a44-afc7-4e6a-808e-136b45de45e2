<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service\Compute;

class InterconnectMacsecConfigPreSharedKey extends \Google\Model
{
  /**
   * @var string
   */
  public $cak;
  /**
   * @var string
   */
  public $ckn;
  /**
   * @var string
   */
  public $name;
  /**
   * @var string
   */
  public $startTime;

  /**
   * @param string
   */
  public function setCak($cak)
  {
    $this->cak = $cak;
  }
  /**
   * @return string
   */
  public function getCak()
  {
    return $this->cak;
  }
  /**
   * @param string
   */
  public function setCkn($ckn)
  {
    $this->ckn = $ckn;
  }
  /**
   * @return string
   */
  public function getCkn()
  {
    return $this->ckn;
  }
  /**
   * @param string
   */
  public function setName($name)
  {
    $this->name = $name;
  }
  /**
   * @return string
   */
  public function getName()
  {
    return $this->name;
  }
  /**
   * @param string
   */
  public function setStartTime($startTime)
  {
    $this->startTime = $startTime;
  }
  /**
   * @return string
   */
  public function getStartTime()
  {
    return $this->startTime;
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(InterconnectMacsecConfigPreSharedKey::class, 'Google_Service_Compute_InterconnectMacsecConfigPreSharedKey');
