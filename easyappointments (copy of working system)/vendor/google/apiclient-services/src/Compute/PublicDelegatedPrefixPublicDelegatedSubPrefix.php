<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service\Compute;

class PublicDelegatedPrefixPublicDelegatedSubPrefix extends \Google\Model
{
  /**
   * @var int
   */
  public $allocatablePrefixLength;
  /**
   * @var string
   */
  public $delegateeProject;
  /**
   * @var string
   */
  public $description;
  /**
   * @var string
   */
  public $ipCidrRange;
  /**
   * @var bool
   */
  public $isAddress;
  /**
   * @var string
   */
  public $mode;
  /**
   * @var string
   */
  public $name;
  /**
   * @var string
   */
  public $region;
  /**
   * @var string
   */
  public $status;

  /**
   * @param int
   */
  public function setAllocatablePrefixLength($allocatablePrefixLength)
  {
    $this->allocatablePrefixLength = $allocatablePrefixLength;
  }
  /**
   * @return int
   */
  public function getAllocatablePrefixLength()
  {
    return $this->allocatablePrefixLength;
  }
  /**
   * @param string
   */
  public function setDelegateeProject($delegateeProject)
  {
    $this->delegateeProject = $delegateeProject;
  }
  /**
   * @return string
   */
  public function getDelegateeProject()
  {
    return $this->delegateeProject;
  }
  /**
   * @param string
   */
  public function setDescription($description)
  {
    $this->description = $description;
  }
  /**
   * @return string
   */
  public function getDescription()
  {
    return $this->description;
  }
  /**
   * @param string
   */
  public function setIpCidrRange($ipCidrRange)
  {
    $this->ipCidrRange = $ipCidrRange;
  }
  /**
   * @return string
   */
  public function getIpCidrRange()
  {
    return $this->ipCidrRange;
  }
  /**
   * @param bool
   */
  public function setIsAddress($isAddress)
  {
    $this->isAddress = $isAddress;
  }
  /**
   * @return bool
   */
  public function getIsAddress()
  {
    return $this->isAddress;
  }
  /**
   * @param string
   */
  public function setMode($mode)
  {
    $this->mode = $mode;
  }
  /**
   * @return string
   */
  public function getMode()
  {
    return $this->mode;
  }
  /**
   * @param string
   */
  public function setName($name)
  {
    $this->name = $name;
  }
  /**
   * @return string
   */
  public function getName()
  {
    return $this->name;
  }
  /**
   * @param string
   */
  public function setRegion($region)
  {
    $this->region = $region;
  }
  /**
   * @return string
   */
  public function getRegion()
  {
    return $this->region;
  }
  /**
   * @param string
   */
  public function setStatus($status)
  {
    $this->status = $status;
  }
  /**
   * @return string
   */
  public function getStatus()
  {
    return $this->status;
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(PublicDelegatedPrefixPublicDelegatedSubPrefix::class, 'Google_Service_Compute_PublicDelegatedPrefixPublicDelegatedSubPrefix');
