<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service\Compute;

class InstanceGroupManagersSetTargetPoolsRequest extends \Google\Collection
{
  protected $collection_key = 'targetPools';
  /**
   * @var string
   */
  public $fingerprint;
  /**
   * @var string[]
   */
  public $targetPools;

  /**
   * @param string
   */
  public function setFingerprint($fingerprint)
  {
    $this->fingerprint = $fingerprint;
  }
  /**
   * @return string
   */
  public function getFingerprint()
  {
    return $this->fingerprint;
  }
  /**
   * @param string[]
   */
  public function setTargetPools($targetPools)
  {
    $this->targetPools = $targetPools;
  }
  /**
   * @return string[]
   */
  public function getTargetPools()
  {
    return $this->targetPools;
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(InstanceGroupManagersSetTargetPoolsRequest::class, 'Google_Service_Compute_InstanceGroupManagersSetTargetPoolsRequest');
