<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service\Compute;

class InstanceGroupManagerInstanceLifecyclePolicy extends \Google\Model
{
  /**
   * @var string
   */
  public $defaultActionOnFailure;
  /**
   * @var string
   */
  public $forceUpdateOnRepair;

  /**
   * @param string
   */
  public function setDefaultActionOnFailure($defaultActionOnFailure)
  {
    $this->defaultActionOnFailure = $defaultActionOnFailure;
  }
  /**
   * @return string
   */
  public function getDefaultActionOnFailure()
  {
    return $this->defaultActionOnFailure;
  }
  /**
   * @param string
   */
  public function setForceUpdateOnRepair($forceUpdateOnRepair)
  {
    $this->forceUpdateOnRepair = $forceUpdateOnRepair;
  }
  /**
   * @return string
   */
  public function getForceUpdateOnRepair()
  {
    return $this->forceUpdateOnRepair;
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(InstanceGroupManagerInstanceLifecyclePolicy::class, 'Google_Service_Compute_InstanceGroupManagerInstanceLifecyclePolicy');
