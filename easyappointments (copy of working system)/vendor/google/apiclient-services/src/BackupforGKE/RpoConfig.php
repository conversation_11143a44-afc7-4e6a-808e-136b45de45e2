<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service\BackupforGKE;

class RpoConfig extends \Google\Collection
{
  protected $collection_key = 'exclusionWindows';
  protected $exclusionWindowsType = ExclusionWindow::class;
  protected $exclusionWindowsDataType = 'array';
  /**
   * @var int
   */
  public $targetRpoMinutes;

  /**
   * @param ExclusionWindow[]
   */
  public function setExclusionWindows($exclusionWindows)
  {
    $this->exclusionWindows = $exclusionWindows;
  }
  /**
   * @return ExclusionWindow[]
   */
  public function getExclusionWindows()
  {
    return $this->exclusionWindows;
  }
  /**
   * @param int
   */
  public function setTargetRpoMinutes($targetRpoMinutes)
  {
    $this->targetRpoMinutes = $targetRpoMinutes;
  }
  /**
   * @return int
   */
  public function getTargetRpoMinutes()
  {
    return $this->targetRpoMinutes;
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(RpoConfig::class, 'Google_Service_BackupforGKE_RpoConfig');
