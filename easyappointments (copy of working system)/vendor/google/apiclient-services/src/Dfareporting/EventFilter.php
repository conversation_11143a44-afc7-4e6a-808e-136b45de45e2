<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service\Dfareporting;

class EventFilter extends \Google\Model
{
  protected $dimensionFilterType = PathReportDimensionValue::class;
  protected $dimensionFilterDataType = '';
  /**
   * @var string
   */
  public $kind;
  protected $uvarFilterType = UvarFilter::class;
  protected $uvarFilterDataType = '';

  /**
   * @param PathReportDimensionValue
   */
  public function setDimensionFilter(PathReportDimensionValue $dimensionFilter)
  {
    $this->dimensionFilter = $dimensionFilter;
  }
  /**
   * @return PathReportDimensionValue
   */
  public function getDimensionFilter()
  {
    return $this->dimensionFilter;
  }
  /**
   * @param string
   */
  public function setKind($kind)
  {
    $this->kind = $kind;
  }
  /**
   * @return string
   */
  public function getKind()
  {
    return $this->kind;
  }
  /**
   * @param UvarFilter
   */
  public function setUvarFilter(UvarFilter $uvarFilter)
  {
    $this->uvarFilter = $uvarFilter;
  }
  /**
   * @return UvarFilter
   */
  public function getUvarFilter()
  {
    return $this->uvarFilter;
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(EventFilter::class, 'Google_Service_Dfareporting_EventFilter');
