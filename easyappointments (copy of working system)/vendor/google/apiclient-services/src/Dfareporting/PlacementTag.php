<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service\Dfareporting;

class PlacementTag extends \Google\Collection
{
  protected $collection_key = 'tagDatas';
  /**
   * @var string
   */
  public $placementId;
  protected $tagDatasType = TagData::class;
  protected $tagDatasDataType = 'array';

  /**
   * @param string
   */
  public function setPlacementId($placementId)
  {
    $this->placementId = $placementId;
  }
  /**
   * @return string
   */
  public function getPlacementId()
  {
    return $this->placementId;
  }
  /**
   * @param TagData[]
   */
  public function setTagDatas($tagDatas)
  {
    $this->tagDatas = $tagDatas;
  }
  /**
   * @return TagData[]
   */
  public function getTagDatas()
  {
    return $this->tagDatas;
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(PlacementTag::class, 'Google_Service_Dfareporting_PlacementTag');
