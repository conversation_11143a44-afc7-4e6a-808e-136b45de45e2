<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service\Dfareporting;

class OptimizationActivity extends \Google\Model
{
  /**
   * @var string
   */
  public $floodlightActivityId;
  protected $floodlightActivityIdDimensionValueType = DimensionValue::class;
  protected $floodlightActivityIdDimensionValueDataType = '';
  /**
   * @var int
   */
  public $weight;

  /**
   * @param string
   */
  public function setFloodlightActivityId($floodlightActivityId)
  {
    $this->floodlightActivityId = $floodlightActivityId;
  }
  /**
   * @return string
   */
  public function getFloodlightActivityId()
  {
    return $this->floodlightActivityId;
  }
  /**
   * @param DimensionValue
   */
  public function setFloodlightActivityIdDimensionValue(DimensionValue $floodlightActivityIdDimensionValue)
  {
    $this->floodlightActivityIdDimensionValue = $floodlightActivityIdDimensionValue;
  }
  /**
   * @return DimensionValue
   */
  public function getFloodlightActivityIdDimensionValue()
  {
    return $this->floodlightActivityIdDimensionValue;
  }
  /**
   * @param int
   */
  public function setWeight($weight)
  {
    $this->weight = $weight;
  }
  /**
   * @return int
   */
  public function getWeight()
  {
    return $this->weight;
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(OptimizationActivity::class, 'Google_Service_Dfareporting_OptimizationActivity');
