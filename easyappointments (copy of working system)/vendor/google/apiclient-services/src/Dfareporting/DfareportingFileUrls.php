<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service\Dfareporting;

class DfareportingFileUrls extends \Google\Model
{
  /**
   * @var string
   */
  public $apiUrl;
  /**
   * @var string
   */
  public $browserUrl;

  /**
   * @param string
   */
  public function setApiUrl($apiUrl)
  {
    $this->apiUrl = $apiUrl;
  }
  /**
   * @return string
   */
  public function getApiUrl()
  {
    return $this->apiUrl;
  }
  /**
   * @param string
   */
  public function setBrowserUrl($browserUrl)
  {
    $this->browserUrl = $browserUrl;
  }
  /**
   * @return string
   */
  public function getBrowserUrl()
  {
    return $this->browserUrl;
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(DfareportingFileUrls::class, 'Google_Service_Dfareporting_DfareportingFileUrls');
