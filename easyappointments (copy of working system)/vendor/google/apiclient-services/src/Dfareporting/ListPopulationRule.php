<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service\Dfareporting;

class ListPopulationRule extends \Google\Collection
{
  protected $collection_key = 'listPopulationClauses';
  /**
   * @var string
   */
  public $floodlightActivityId;
  /**
   * @var string
   */
  public $floodlightActivityName;
  protected $listPopulationClausesType = ListPopulationClause::class;
  protected $listPopulationClausesDataType = 'array';

  /**
   * @param string
   */
  public function setFloodlightActivityId($floodlightActivityId)
  {
    $this->floodlightActivityId = $floodlightActivityId;
  }
  /**
   * @return string
   */
  public function getFloodlightActivityId()
  {
    return $this->floodlightActivityId;
  }
  /**
   * @param string
   */
  public function setFloodlightActivityName($floodlightActivityName)
  {
    $this->floodlightActivityName = $floodlightActivityName;
  }
  /**
   * @return string
   */
  public function getFloodlightActivityName()
  {
    return $this->floodlightActivityName;
  }
  /**
   * @param ListPopulationClause[]
   */
  public function setListPopulationClauses($listPopulationClauses)
  {
    $this->listPopulationClauses = $listPopulationClauses;
  }
  /**
   * @return ListPopulationClause[]
   */
  public function getListPopulationClauses()
  {
    return $this->listPopulationClauses;
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(ListPopulationRule::class, 'Google_Service_Dfareporting_ListPopulationRule');
