<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service\Dfareporting;

class SizesListResponse extends \Google\Collection
{
  protected $collection_key = 'sizes';
  /**
   * @var string
   */
  public $kind;
  protected $sizesType = Size::class;
  protected $sizesDataType = 'array';

  /**
   * @param string
   */
  public function setKind($kind)
  {
    $this->kind = $kind;
  }
  /**
   * @return string
   */
  public function getKind()
  {
    return $this->kind;
  }
  /**
   * @param Size[]
   */
  public function setSizes($sizes)
  {
    $this->sizes = $sizes;
  }
  /**
   * @return Size[]
   */
  public function getSizes()
  {
    return $this->sizes;
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(SizesListResponse::class, 'Google_Service_Dfareporting_SizesListResponse');
