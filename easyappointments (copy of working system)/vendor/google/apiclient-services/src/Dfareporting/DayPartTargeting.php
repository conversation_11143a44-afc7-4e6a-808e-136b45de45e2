<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service\Dfareporting;

class DayPartTargeting extends \Google\Collection
{
  protected $collection_key = 'hoursOfDay';
  /**
   * @var string[]
   */
  public $daysOfWeek;
  /**
   * @var int[]
   */
  public $hoursOfDay;
  /**
   * @var bool
   */
  public $userLocalTime;

  /**
   * @param string[]
   */
  public function setDaysOfWeek($daysOfWeek)
  {
    $this->daysOfWeek = $daysOfWeek;
  }
  /**
   * @return string[]
   */
  public function getDaysOfWeek()
  {
    return $this->daysOfWeek;
  }
  /**
   * @param int[]
   */
  public function setHoursOfDay($hoursOfDay)
  {
    $this->hoursOfDay = $hoursOfDay;
  }
  /**
   * @return int[]
   */
  public function getHoursOfDay()
  {
    return $this->hoursOfDay;
  }
  /**
   * @param bool
   */
  public function setUserLocalTime($userLocalTime)
  {
    $this->userLocalTime = $userLocalTime;
  }
  /**
   * @return bool
   */
  public function getUserLocalTime()
  {
    return $this->userLocalTime;
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(DayPartTargeting::class, 'Google_Service_Dfareporting_DayPartTargeting');
