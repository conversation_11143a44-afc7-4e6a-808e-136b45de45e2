<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service\YouTube\Resource;

use Google\Service\YouTube\InvideoBranding;

/**
 * The "watermarks" collection of methods.
 * Typical usage is:
 *  <code>
 *   $youtubeService = new Google\Service\YouTube(...);
 *   $watermarks = $youtubeService->watermarks;
 *  </code>
 */
class Watermarks extends \Google\Service\Resource
{
  /**
   * Allows upload of watermark image and setting it for a channel.
   * (watermarks.set)
   *
   * @param string $channelId
   * @param InvideoBranding $postBody
   * @param array $optParams Optional parameters.
   *
   * @opt_param string onBehalfOfContentOwner *Note:* This parameter is intended
   * exclusively for YouTube content partners. The *onBehalfOfContentOwner*
   * parameter indicates that the request's authorization credentials identify a
   * YouTube CMS user who is acting on behalf of the content owner specified in
   * the parameter value. This parameter is intended for YouTube content partners
   * that own and manage many different YouTube channels. It allows content owners
   * to authenticate once and get access to all their video and channel data,
   * without having to provide authentication credentials for each individual
   * channel. The CMS account that the user authenticates with must be linked to
   * the specified YouTube content owner.
   * @throws \Google\Service\Exception
   */
  public function set($channelId, InvideoBranding $postBody, $optParams = [])
  {
    $params = ['channelId' => $channelId, 'postBody' => $postBody];
    $params = array_merge($params, $optParams);
    return $this->call('set', [$params]);
  }
  /**
   * Allows removal of channel watermark. (watermarks.unsetWatermarks)
   *
   * @param string $channelId
   * @param array $optParams Optional parameters.
   *
   * @opt_param string onBehalfOfContentOwner *Note:* This parameter is intended
   * exclusively for YouTube content partners. The *onBehalfOfContentOwner*
   * parameter indicates that the request's authorization credentials identify a
   * YouTube CMS user who is acting on behalf of the content owner specified in
   * the parameter value. This parameter is intended for YouTube content partners
   * that own and manage many different YouTube channels. It allows content owners
   * to authenticate once and get access to all their video and channel data,
   * without having to provide authentication credentials for each individual
   * channel. The CMS account that the user authenticates with must be linked to
   * the specified YouTube content owner.
   * @throws \Google\Service\Exception
   */
  public function unsetWatermarks($channelId, $optParams = [])
  {
    $params = ['channelId' => $channelId];
    $params = array_merge($params, $optParams);
    return $this->call('unset', [$params]);
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(Watermarks::class, 'Google_Service_YouTube_Resource_Watermarks');
