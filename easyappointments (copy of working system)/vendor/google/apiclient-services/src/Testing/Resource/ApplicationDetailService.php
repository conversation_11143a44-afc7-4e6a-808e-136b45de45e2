<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service\Testing\Resource;

use Google\Service\Testing\FileReference;
use Google\Service\Testing\GetApkDetailsResponse;

/**
 * The "applicationDetailService" collection of methods.
 * Typical usage is:
 *  <code>
 *   $testingService = new Google\Service\Testing(...);
 *   $applicationDetailService = $testingService->applicationDetailService;
 *  </code>
 */
class ApplicationDetailService extends \Google\Service\Resource
{
  /**
   * Gets the details of an Android application APK.
   * (applicationDetailService.getApkDetails)
   *
   * @param FileReference $postBody
   * @param array $optParams Optional parameters.
   *
   * @opt_param string bundleLocation.gcsPath A path to a file in Google Cloud
   * Storage. Example: gs://build-app-1414623860166/app%40debug-unaligned.apk
   * These paths are expected to be url encoded (percent encoding)
   * @return GetApkDetailsResponse
   * @throws \Google\Service\Exception
   */
  public function getApkDetails(FileReference $postBody, $optParams = [])
  {
    $params = ['postBody' => $postBody];
    $params = array_merge($params, $optParams);
    return $this->call('getApkDetails', [$params], GetApkDetailsResponse::class);
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(ApplicationDetailService::class, 'Google_Service_Testing_Resource_ApplicationDetailService');
