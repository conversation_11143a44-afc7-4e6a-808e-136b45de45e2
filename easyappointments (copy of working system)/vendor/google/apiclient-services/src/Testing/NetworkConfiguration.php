<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service\Testing;

class NetworkConfiguration extends \Google\Model
{
  protected $downRuleType = TrafficRule::class;
  protected $downRuleDataType = '';
  /**
   * @var string
   */
  public $id;
  protected $upRuleType = TrafficRule::class;
  protected $upRuleDataType = '';

  /**
   * @param TrafficRule
   */
  public function setDownRule(TrafficRule $downRule)
  {
    $this->downRule = $downRule;
  }
  /**
   * @return TrafficRule
   */
  public function getDownRule()
  {
    return $this->downRule;
  }
  /**
   * @param string
   */
  public function setId($id)
  {
    $this->id = $id;
  }
  /**
   * @return string
   */
  public function getId()
  {
    return $this->id;
  }
  /**
   * @param TrafficRule
   */
  public function setUpRule(TrafficRule $upRule)
  {
    $this->upRule = $upRule;
  }
  /**
   * @return TrafficRule
   */
  public function getUpRule()
  {
    return $this->upRule;
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(NetworkConfiguration::class, 'Google_Service_Testing_NetworkConfiguration');
