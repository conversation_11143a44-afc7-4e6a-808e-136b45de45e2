<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service\Spanner;

class Database extends \Google\Collection
{
  protected $collection_key = 'encryptionInfo';
  /**
   * @var string
   */
  public $createTime;
  /**
   * @var string
   */
  public $databaseDialect;
  /**
   * @var string
   */
  public $defaultLeader;
  /**
   * @var string
   */
  public $earliestVersionTime;
  /**
   * @var bool
   */
  public $enableDropProtection;
  protected $encryptionConfigType = EncryptionConfig::class;
  protected $encryptionConfigDataType = '';
  protected $encryptionInfoType = EncryptionInfo::class;
  protected $encryptionInfoDataType = 'array';
  /**
   * @var string
   */
  public $name;
  protected $quorumInfoType = QuorumInfo::class;
  protected $quorumInfoDataType = '';
  /**
   * @var bool
   */
  public $reconciling;
  protected $restoreInfoType = RestoreInfo::class;
  protected $restoreInfoDataType = '';
  /**
   * @var string
   */
  public $state;
  /**
   * @var string
   */
  public $versionRetentionPeriod;

  /**
   * @param string
   */
  public function setCreateTime($createTime)
  {
    $this->createTime = $createTime;
  }
  /**
   * @return string
   */
  public function getCreateTime()
  {
    return $this->createTime;
  }
  /**
   * @param string
   */
  public function setDatabaseDialect($databaseDialect)
  {
    $this->databaseDialect = $databaseDialect;
  }
  /**
   * @return string
   */
  public function getDatabaseDialect()
  {
    return $this->databaseDialect;
  }
  /**
   * @param string
   */
  public function setDefaultLeader($defaultLeader)
  {
    $this->defaultLeader = $defaultLeader;
  }
  /**
   * @return string
   */
  public function getDefaultLeader()
  {
    return $this->defaultLeader;
  }
  /**
   * @param string
   */
  public function setEarliestVersionTime($earliestVersionTime)
  {
    $this->earliestVersionTime = $earliestVersionTime;
  }
  /**
   * @return string
   */
  public function getEarliestVersionTime()
  {
    return $this->earliestVersionTime;
  }
  /**
   * @param bool
   */
  public function setEnableDropProtection($enableDropProtection)
  {
    $this->enableDropProtection = $enableDropProtection;
  }
  /**
   * @return bool
   */
  public function getEnableDropProtection()
  {
    return $this->enableDropProtection;
  }
  /**
   * @param EncryptionConfig
   */
  public function setEncryptionConfig(EncryptionConfig $encryptionConfig)
  {
    $this->encryptionConfig = $encryptionConfig;
  }
  /**
   * @return EncryptionConfig
   */
  public function getEncryptionConfig()
  {
    return $this->encryptionConfig;
  }
  /**
   * @param EncryptionInfo[]
   */
  public function setEncryptionInfo($encryptionInfo)
  {
    $this->encryptionInfo = $encryptionInfo;
  }
  /**
   * @return EncryptionInfo[]
   */
  public function getEncryptionInfo()
  {
    return $this->encryptionInfo;
  }
  /**
   * @param string
   */
  public function setName($name)
  {
    $this->name = $name;
  }
  /**
   * @return string
   */
  public function getName()
  {
    return $this->name;
  }
  /**
   * @param QuorumInfo
   */
  public function setQuorumInfo(QuorumInfo $quorumInfo)
  {
    $this->quorumInfo = $quorumInfo;
  }
  /**
   * @return QuorumInfo
   */
  public function getQuorumInfo()
  {
    return $this->quorumInfo;
  }
  /**
   * @param bool
   */
  public function setReconciling($reconciling)
  {
    $this->reconciling = $reconciling;
  }
  /**
   * @return bool
   */
  public function getReconciling()
  {
    return $this->reconciling;
  }
  /**
   * @param RestoreInfo
   */
  public function setRestoreInfo(RestoreInfo $restoreInfo)
  {
    $this->restoreInfo = $restoreInfo;
  }
  /**
   * @return RestoreInfo
   */
  public function getRestoreInfo()
  {
    return $this->restoreInfo;
  }
  /**
   * @param string
   */
  public function setState($state)
  {
    $this->state = $state;
  }
  /**
   * @return string
   */
  public function getState()
  {
    return $this->state;
  }
  /**
   * @param string
   */
  public function setVersionRetentionPeriod($versionRetentionPeriod)
  {
    $this->versionRetentionPeriod = $versionRetentionPeriod;
  }
  /**
   * @return string
   */
  public function getVersionRetentionPeriod()
  {
    return $this->versionRetentionPeriod;
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(Database::class, 'Google_Service_Spanner_Database');
