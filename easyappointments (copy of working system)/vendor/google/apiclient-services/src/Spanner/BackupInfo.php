<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service\Spanner;

class BackupInfo extends \Google\Model
{
  /**
   * @var string
   */
  public $backup;
  /**
   * @var string
   */
  public $createTime;
  /**
   * @var string
   */
  public $sourceDatabase;
  /**
   * @var string
   */
  public $versionTime;

  /**
   * @param string
   */
  public function setBackup($backup)
  {
    $this->backup = $backup;
  }
  /**
   * @return string
   */
  public function getBackup()
  {
    return $this->backup;
  }
  /**
   * @param string
   */
  public function setCreateTime($createTime)
  {
    $this->createTime = $createTime;
  }
  /**
   * @return string
   */
  public function getCreateTime()
  {
    return $this->createTime;
  }
  /**
   * @param string
   */
  public function setSourceDatabase($sourceDatabase)
  {
    $this->sourceDatabase = $sourceDatabase;
  }
  /**
   * @return string
   */
  public function getSourceDatabase()
  {
    return $this->sourceDatabase;
  }
  /**
   * @param string
   */
  public function setVersionTime($versionTime)
  {
    $this->versionTime = $versionTime;
  }
  /**
   * @return string
   */
  public function getVersionTime()
  {
    return $this->versionTime;
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(BackupInfo::class, 'Google_Service_Spanner_BackupInfo');
