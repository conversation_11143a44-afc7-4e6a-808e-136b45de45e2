<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service\Spanner;

class DerivedMetric extends \Google\Model
{
  protected $denominatorType = LocalizedString::class;
  protected $denominatorDataType = '';
  protected $numeratorType = LocalizedString::class;
  protected $numeratorDataType = '';

  /**
   * @param LocalizedString
   */
  public function setDenominator(LocalizedString $denominator)
  {
    $this->denominator = $denominator;
  }
  /**
   * @return LocalizedString
   */
  public function getDenominator()
  {
    return $this->denominator;
  }
  /**
   * @param LocalizedString
   */
  public function setNumerator(LocalizedString $numerator)
  {
    $this->numerator = $numerator;
  }
  /**
   * @return LocalizedString
   */
  public function getNumerator()
  {
    return $this->numerator;
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(DerivedMetric::class, 'Google_Service_Spanner_DerivedMetric');
