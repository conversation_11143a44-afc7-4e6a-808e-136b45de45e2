<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service\Spanner;

class DdlStatementActionInfo extends \Google\Collection
{
  protected $collection_key = 'entityNames';
  /**
   * @var string
   */
  public $action;
  /**
   * @var string[]
   */
  public $entityNames;
  /**
   * @var string
   */
  public $entityType;

  /**
   * @param string
   */
  public function setAction($action)
  {
    $this->action = $action;
  }
  /**
   * @return string
   */
  public function getAction()
  {
    return $this->action;
  }
  /**
   * @param string[]
   */
  public function setEntityNames($entityNames)
  {
    $this->entityNames = $entityNames;
  }
  /**
   * @return string[]
   */
  public function getEntityNames()
  {
    return $this->entityNames;
  }
  /**
   * @param string
   */
  public function setEntityType($entityType)
  {
    $this->entityType = $entityType;
  }
  /**
   * @return string
   */
  public function getEntityType()
  {
    return $this->entityType;
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(DdlStatementActionInfo::class, 'Google_Service_Spanner_DdlStatementActionInfo');
