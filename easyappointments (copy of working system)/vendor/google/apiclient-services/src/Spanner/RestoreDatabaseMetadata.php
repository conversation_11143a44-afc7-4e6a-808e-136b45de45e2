<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service\Spanner;

class RestoreDatabaseMetadata extends \Google\Model
{
  protected $backupInfoType = BackupInfo::class;
  protected $backupInfoDataType = '';
  /**
   * @var string
   */
  public $cancelTime;
  /**
   * @var string
   */
  public $name;
  /**
   * @var string
   */
  public $optimizeDatabaseOperationName;
  protected $progressType = OperationProgress::class;
  protected $progressDataType = '';
  /**
   * @var string
   */
  public $sourceType;

  /**
   * @param BackupInfo
   */
  public function setBackupInfo(BackupInfo $backupInfo)
  {
    $this->backupInfo = $backupInfo;
  }
  /**
   * @return BackupInfo
   */
  public function getBackupInfo()
  {
    return $this->backupInfo;
  }
  /**
   * @param string
   */
  public function setCancelTime($cancelTime)
  {
    $this->cancelTime = $cancelTime;
  }
  /**
   * @return string
   */
  public function getCancelTime()
  {
    return $this->cancelTime;
  }
  /**
   * @param string
   */
  public function setName($name)
  {
    $this->name = $name;
  }
  /**
   * @return string
   */
  public function getName()
  {
    return $this->name;
  }
  /**
   * @param string
   */
  public function setOptimizeDatabaseOperationName($optimizeDatabaseOperationName)
  {
    $this->optimizeDatabaseOperationName = $optimizeDatabaseOperationName;
  }
  /**
   * @return string
   */
  public function getOptimizeDatabaseOperationName()
  {
    return $this->optimizeDatabaseOperationName;
  }
  /**
   * @param OperationProgress
   */
  public function setProgress(OperationProgress $progress)
  {
    $this->progress = $progress;
  }
  /**
   * @return OperationProgress
   */
  public function getProgress()
  {
    return $this->progress;
  }
  /**
   * @param string
   */
  public function setSourceType($sourceType)
  {
    $this->sourceType = $sourceType;
  }
  /**
   * @return string
   */
  public function getSourceType()
  {
    return $this->sourceType;
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(RestoreDatabaseMetadata::class, 'Google_Service_Spanner_RestoreDatabaseMetadata');
