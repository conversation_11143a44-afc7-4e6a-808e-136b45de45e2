<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service\CloudDomains;

class DomainForwarding extends \Google\Model
{
  /**
   * @var bool
   */
  public $pathForwarding;
  /**
   * @var string
   */
  public $pemCertificate;
  /**
   * @var string
   */
  public $redirectType;
  /**
   * @var bool
   */
  public $sslEnabled;
  /**
   * @var string
   */
  public $subdomain;
  /**
   * @var string
   */
  public $targetUri;

  /**
   * @param bool
   */
  public function setPathForwarding($pathForwarding)
  {
    $this->pathForwarding = $pathForwarding;
  }
  /**
   * @return bool
   */
  public function getPathForwarding()
  {
    return $this->pathForwarding;
  }
  /**
   * @param string
   */
  public function setPemCertificate($pemCertificate)
  {
    $this->pemCertificate = $pemCertificate;
  }
  /**
   * @return string
   */
  public function getPemCertificate()
  {
    return $this->pemCertificate;
  }
  /**
   * @param string
   */
  public function setRedirectType($redirectType)
  {
    $this->redirectType = $redirectType;
  }
  /**
   * @return string
   */
  public function getRedirectType()
  {
    return $this->redirectType;
  }
  /**
   * @param bool
   */
  public function setSslEnabled($sslEnabled)
  {
    $this->sslEnabled = $sslEnabled;
  }
  /**
   * @return bool
   */
  public function getSslEnabled()
  {
    return $this->sslEnabled;
  }
  /**
   * @param string
   */
  public function setSubdomain($subdomain)
  {
    $this->subdomain = $subdomain;
  }
  /**
   * @return string
   */
  public function getSubdomain()
  {
    return $this->subdomain;
  }
  /**
   * @param string
   */
  public function setTargetUri($targetUri)
  {
    $this->targetUri = $targetUri;
  }
  /**
   * @return string
   */
  public function getTargetUri()
  {
    return $this->targetUri;
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(DomainForwarding::class, 'Google_Service_CloudDomains_DomainForwarding');
