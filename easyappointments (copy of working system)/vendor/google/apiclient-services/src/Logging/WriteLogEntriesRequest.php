<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service\Logging;

class WriteLogEntriesRequest extends \Google\Collection
{
  protected $collection_key = 'entries';
  /**
   * @var bool
   */
  public $dryRun;
  protected $entriesType = LogEntry::class;
  protected $entriesDataType = 'array';
  /**
   * @var string[]
   */
  public $labels;
  /**
   * @var string
   */
  public $logName;
  /**
   * @var bool
   */
  public $partialSuccess;
  protected $resourceType = MonitoredResource::class;
  protected $resourceDataType = '';

  /**
   * @param bool
   */
  public function setDryRun($dryRun)
  {
    $this->dryRun = $dryRun;
  }
  /**
   * @return bool
   */
  public function getDryRun()
  {
    return $this->dryRun;
  }
  /**
   * @param LogEntry[]
   */
  public function setEntries($entries)
  {
    $this->entries = $entries;
  }
  /**
   * @return LogEntry[]
   */
  public function getEntries()
  {
    return $this->entries;
  }
  /**
   * @param string[]
   */
  public function setLabels($labels)
  {
    $this->labels = $labels;
  }
  /**
   * @return string[]
   */
  public function getLabels()
  {
    return $this->labels;
  }
  /**
   * @param string
   */
  public function setLogName($logName)
  {
    $this->logName = $logName;
  }
  /**
   * @return string
   */
  public function getLogName()
  {
    return $this->logName;
  }
  /**
   * @param bool
   */
  public function setPartialSuccess($partialSuccess)
  {
    $this->partialSuccess = $partialSuccess;
  }
  /**
   * @return bool
   */
  public function getPartialSuccess()
  {
    return $this->partialSuccess;
  }
  /**
   * @param MonitoredResource
   */
  public function setResource(MonitoredResource $resource)
  {
    $this->resource = $resource;
  }
  /**
   * @return MonitoredResource
   */
  public function getResource()
  {
    return $this->resource;
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(WriteLogEntriesRequest::class, 'Google_Service_Logging_WriteLogEntriesRequest');
