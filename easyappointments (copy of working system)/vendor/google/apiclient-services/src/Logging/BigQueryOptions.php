<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service\Logging;

class BigQueryOptions extends \Google\Model
{
  /**
   * @var bool
   */
  public $usePartitionedTables;
  /**
   * @var bool
   */
  public $usesTimestampColumnPartitioning;

  /**
   * @param bool
   */
  public function setUsePartitionedTables($usePartitionedTables)
  {
    $this->usePartitionedTables = $usePartitionedTables;
  }
  /**
   * @return bool
   */
  public function getUsePartitionedTables()
  {
    return $this->usePartitionedTables;
  }
  /**
   * @param bool
   */
  public function setUsesTimestampColumnPartitioning($usesTimestampColumnPartitioning)
  {
    $this->usesTimestampColumnPartitioning = $usesTimestampColumnPartitioning;
  }
  /**
   * @return bool
   */
  public function getUsesTimestampColumnPartitioning()
  {
    return $this->usesTimestampColumnPartitioning;
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(BigQueryOptions::class, 'Google_Service_Logging_BigQueryOptions');
