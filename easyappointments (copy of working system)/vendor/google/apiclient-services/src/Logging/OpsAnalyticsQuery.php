<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service\Logging;

class OpsAnalyticsQuery extends \Google\Model
{
  /**
   * @var string
   */
  public $sqlQueryText;

  /**
   * @param string
   */
  public function setSqlQueryText($sqlQueryText)
  {
    $this->sqlQueryText = $sqlQueryText;
  }
  /**
   * @return string
   */
  public function getSqlQueryText()
  {
    return $this->sqlQueryText;
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(OpsAnalyticsQuery::class, 'Google_Service_Logging_OpsAnalyticsQuery');
