<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service\Cloudbilling;

class Category extends \Google\Model
{
  /**
   * @var string
   */
  public $resourceFamily;
  /**
   * @var string
   */
  public $resourceGroup;
  /**
   * @var string
   */
  public $serviceDisplayName;
  /**
   * @var string
   */
  public $usageType;

  /**
   * @param string
   */
  public function setResourceFamily($resourceFamily)
  {
    $this->resourceFamily = $resourceFamily;
  }
  /**
   * @return string
   */
  public function getResourceFamily()
  {
    return $this->resourceFamily;
  }
  /**
   * @param string
   */
  public function setResourceGroup($resourceGroup)
  {
    $this->resourceGroup = $resourceGroup;
  }
  /**
   * @return string
   */
  public function getResourceGroup()
  {
    return $this->resourceGroup;
  }
  /**
   * @param string
   */
  public function setServiceDisplayName($serviceDisplayName)
  {
    $this->serviceDisplayName = $serviceDisplayName;
  }
  /**
   * @return string
   */
  public function getServiceDisplayName()
  {
    return $this->serviceDisplayName;
  }
  /**
   * @param string
   */
  public function setUsageType($usageType)
  {
    $this->usageType = $usageType;
  }
  /**
   * @return string
   */
  public function getUsageType()
  {
    return $this->usageType;
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(Category::class, 'Google_Service_Cloudbilling_Category');
