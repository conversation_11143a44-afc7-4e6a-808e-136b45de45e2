<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service\Cloudbilling;

class BillingAccount extends \Google\Model
{
  /**
   * @var string
   */
  public $currencyCode;
  /**
   * @var string
   */
  public $displayName;
  /**
   * @var string
   */
  public $masterBillingAccount;
  /**
   * @var string
   */
  public $name;
  /**
   * @var bool
   */
  public $open;
  /**
   * @var string
   */
  public $parent;

  /**
   * @param string
   */
  public function setCurrencyCode($currencyCode)
  {
    $this->currencyCode = $currencyCode;
  }
  /**
   * @return string
   */
  public function getCurrencyCode()
  {
    return $this->currencyCode;
  }
  /**
   * @param string
   */
  public function setDisplayName($displayName)
  {
    $this->displayName = $displayName;
  }
  /**
   * @return string
   */
  public function getDisplayName()
  {
    return $this->displayName;
  }
  /**
   * @param string
   */
  public function setMasterBillingAccount($masterBillingAccount)
  {
    $this->masterBillingAccount = $masterBillingAccount;
  }
  /**
   * @return string
   */
  public function getMasterBillingAccount()
  {
    return $this->masterBillingAccount;
  }
  /**
   * @param string
   */
  public function setName($name)
  {
    $this->name = $name;
  }
  /**
   * @return string
   */
  public function getName()
  {
    return $this->name;
  }
  /**
   * @param bool
   */
  public function setOpen($open)
  {
    $this->open = $open;
  }
  /**
   * @return bool
   */
  public function getOpen()
  {
    return $this->open;
  }
  /**
   * @param string
   */
  public function setParent($parent)
  {
    $this->parent = $parent;
  }
  /**
   * @return string
   */
  public function getParent()
  {
    return $this->parent;
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(BillingAccount::class, 'Google_Service_Cloudbilling_BillingAccount');
