<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service\Cloudchannel;

class GoogleCloudChannelV1CloudIdentityInfo extends \Google\Model
{
  /**
   * @var string
   */
  public $adminConsoleUri;
  /**
   * @var string
   */
  public $alternateEmail;
  /**
   * @var string
   */
  public $customerType;
  protected $eduDataType = GoogleCloudChannelV1EduData::class;
  protected $eduDataDataType = '';
  /**
   * @var bool
   */
  public $isDomainVerified;
  /**
   * @var string
   */
  public $languageCode;
  /**
   * @var string
   */
  public $phoneNumber;
  /**
   * @var string
   */
  public $primaryDomain;

  /**
   * @param string
   */
  public function setAdminConsoleUri($adminConsoleUri)
  {
    $this->adminConsoleUri = $adminConsoleUri;
  }
  /**
   * @return string
   */
  public function getAdminConsoleUri()
  {
    return $this->adminConsoleUri;
  }
  /**
   * @param string
   */
  public function setAlternateEmail($alternateEmail)
  {
    $this->alternateEmail = $alternateEmail;
  }
  /**
   * @return string
   */
  public function getAlternateEmail()
  {
    return $this->alternateEmail;
  }
  /**
   * @param string
   */
  public function setCustomerType($customerType)
  {
    $this->customerType = $customerType;
  }
  /**
   * @return string
   */
  public function getCustomerType()
  {
    return $this->customerType;
  }
  /**
   * @param GoogleCloudChannelV1EduData
   */
  public function setEduData(GoogleCloudChannelV1EduData $eduData)
  {
    $this->eduData = $eduData;
  }
  /**
   * @return GoogleCloudChannelV1EduData
   */
  public function getEduData()
  {
    return $this->eduData;
  }
  /**
   * @param bool
   */
  public function setIsDomainVerified($isDomainVerified)
  {
    $this->isDomainVerified = $isDomainVerified;
  }
  /**
   * @return bool
   */
  public function getIsDomainVerified()
  {
    return $this->isDomainVerified;
  }
  /**
   * @param string
   */
  public function setLanguageCode($languageCode)
  {
    $this->languageCode = $languageCode;
  }
  /**
   * @return string
   */
  public function getLanguageCode()
  {
    return $this->languageCode;
  }
  /**
   * @param string
   */
  public function setPhoneNumber($phoneNumber)
  {
    $this->phoneNumber = $phoneNumber;
  }
  /**
   * @return string
   */
  public function getPhoneNumber()
  {
    return $this->phoneNumber;
  }
  /**
   * @param string
   */
  public function setPrimaryDomain($primaryDomain)
  {
    $this->primaryDomain = $primaryDomain;
  }
  /**
   * @return string
   */
  public function getPrimaryDomain()
  {
    return $this->primaryDomain;
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(GoogleCloudChannelV1CloudIdentityInfo::class, 'Google_Service_Cloudchannel_GoogleCloudChannelV1CloudIdentityInfo');
