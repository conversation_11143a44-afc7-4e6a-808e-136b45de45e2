<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service\Cloudchannel;

class GoogleCloudChannelV1PriceByResource extends \Google\Collection
{
  protected $collection_key = 'pricePhases';
  protected $priceType = GoogleCloudChannelV1Price::class;
  protected $priceDataType = '';
  protected $pricePhasesType = GoogleCloudChannelV1PricePhase::class;
  protected $pricePhasesDataType = 'array';
  /**
   * @var string
   */
  public $resourceType;

  /**
   * @param GoogleCloudChannelV1Price
   */
  public function setPrice(GoogleCloudChannelV1Price $price)
  {
    $this->price = $price;
  }
  /**
   * @return GoogleCloudChannelV1Price
   */
  public function getPrice()
  {
    return $this->price;
  }
  /**
   * @param GoogleCloudChannelV1PricePhase[]
   */
  public function setPricePhases($pricePhases)
  {
    $this->pricePhases = $pricePhases;
  }
  /**
   * @return GoogleCloudChannelV1PricePhase[]
   */
  public function getPricePhases()
  {
    return $this->pricePhases;
  }
  /**
   * @param string
   */
  public function setResourceType($resourceType)
  {
    $this->resourceType = $resourceType;
  }
  /**
   * @return string
   */
  public function getResourceType()
  {
    return $this->resourceType;
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(GoogleCloudChannelV1PriceByResource::class, 'Google_Service_Cloudchannel_GoogleCloudChannelV1PriceByResource');
