<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service\DataLabeling;

class GoogleCloudDatalabelingV1beta1ObjectTrackingConfig extends \Google\Model
{
  /**
   * @var string
   */
  public $annotationSpecSet;
  /**
   * @var int
   */
  public $clipLength;
  /**
   * @var int
   */
  public $overlapLength;

  /**
   * @param string
   */
  public function setAnnotationSpecSet($annotationSpecSet)
  {
    $this->annotationSpecSet = $annotationSpecSet;
  }
  /**
   * @return string
   */
  public function getAnnotationSpecSet()
  {
    return $this->annotationSpecSet;
  }
  /**
   * @param int
   */
  public function setClipLength($clipLength)
  {
    $this->clipLength = $clipLength;
  }
  /**
   * @return int
   */
  public function getClipLength()
  {
    return $this->clipLength;
  }
  /**
   * @param int
   */
  public function setOverlapLength($overlapLength)
  {
    $this->overlapLength = $overlapLength;
  }
  /**
   * @return int
   */
  public function getOverlapLength()
  {
    return $this->overlapLength;
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(GoogleCloudDatalabelingV1beta1ObjectTrackingConfig::class, 'Google_Service_DataLabeling_GoogleCloudDatalabelingV1beta1ObjectTrackingConfig');
