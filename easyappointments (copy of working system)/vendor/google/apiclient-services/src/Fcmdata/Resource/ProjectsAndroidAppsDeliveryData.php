<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service\Fcmdata\Resource;

use Google\Service\Fcmdata\GoogleFirebaseFcmDataV1beta1ListAndroidDeliveryDataResponse;

/**
 * The "deliveryData" collection of methods.
 * Typical usage is:
 *  <code>
 *   $fcmdataService = new Google\Service\Fcmdata(...);
 *   $deliveryData = $fcmdataService->projects_androidApps_deliveryData;
 *  </code>
 */
class ProjectsAndroidAppsDeliveryData extends \Google\Service\Resource
{
  /**
   * List aggregate delivery data for the given Android application.
   * (deliveryData.listProjectsAndroidAppsDeliveryData)
   *
   * @param string $parent Required. The application for which to list delivery
   * data. Format: `projects/{project_id}/androidApps/{app_id}`
   * @param array $optParams Optional parameters.
   *
   * @opt_param int pageSize The maximum number of entries to return. The service
   * may return fewer than this value. If unspecified, at most 1,000 entries will
   * be returned. The maximum value is 10,000; values above 10,000 will be capped
   * to 10,000. This default may change over time.
   * @opt_param string pageToken A page token, received from a previous
   * `ListAndroidDeliveryDataRequest` call. Provide this to retrieve the
   * subsequent page. When paginating, all other parameters provided to
   * `ListAndroidDeliveryDataRequest` must match the call that provided the page
   * token.
   * @return GoogleFirebaseFcmDataV1beta1ListAndroidDeliveryDataResponse
   * @throws \Google\Service\Exception
   */
  public function listProjectsAndroidAppsDeliveryData($parent, $optParams = [])
  {
    $params = ['parent' => $parent];
    $params = array_merge($params, $optParams);
    return $this->call('list', [$params], GoogleFirebaseFcmDataV1beta1ListAndroidDeliveryDataResponse::class);
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(ProjectsAndroidAppsDeliveryData::class, 'Google_Service_Fcmdata_Resource_ProjectsAndroidAppsDeliveryData');
