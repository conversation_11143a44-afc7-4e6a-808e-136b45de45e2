{"name": "alextselegidis/easyappointments", "description": "Open Source Web Scheduler", "version": "dev-1.5.0", "homepage": "https://easyappointments.org", "type": "project", "license": "GPL-3.0", "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "support": {"issues": "https://github.com/alextselegidis/easyappointments/issues", "forum": "https://groups.google.com/forum/#!forum/easy-appointments", "wiki": "https://easyappointments.org/docs", "source": "https://github.com/alextselegidis/easyappointments"}, "keywords": ["calendar", "scheduler", "appointments", "events", "dates", "google", "services"], "minimum-stability": "stable", "require": {"php": ">=8.1", "ext-curl": "*", "ext-json": "*", "ext-mbstring": "*", "ext-gd": "*", "ext-simplexml": "*", "ext-fileinfo": "*", "gregwar/captcha": "^1.1.9", "jsvrcek/ics": "^0.8.4", "monolog/monolog": "^2.8.0", "google/apiclient": "^2.12.6", "guzzlehttp/guzzle": "^7.9.2", "sabre/vobject": "^4.5", "ezyang/htmlpurifier": "^4.17", "symfony/finder": "^6.4", "phpmailer/phpmailer": "^6.9"}, "require-dev": {"roave/security-advisories": "dev-master", "phpunit/phpunit": "^9.6"}, "scripts": {"test": "APP_ENV=testing php vendor/bin/phpunit"}, "autoload": {"psr-4": {"Tests\\": "tests/"}}}